{"format": 1, "restore": {"E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\OrderFlowCore.Web.csproj": {}}, "projects": {"E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj", "projectName": "OrderFlowCore.Application", "projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj": {"projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[8.0.7, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "itext7": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj", "projectName": "OrderFlowCore.Core", "projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj", "projectName": "OrderFlowCore.Infrastructure", "projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj": {"projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj"}, "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj": {"projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\OrderFlowCore.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\OrderFlowCore.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\OrderFlowCore.Web.csproj", "projectName": "OrderFlowCore.Web", "projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\OrderFlowCore.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj": {"projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\OrderFlowCore.Application.csproj"}, "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj": {"projectPath": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}