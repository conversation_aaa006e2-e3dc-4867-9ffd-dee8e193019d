using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IOrderRepository
{
    Task<int> AddAsync(OrdersTable order);
    Task<int> UpdateAsync(OrdersTable order);
    Task<OrdersTable> GetByIdAsync(int id);
    Task<List<OrdersTable>> GetAllAsync();
    Task<OrdersTable?> GetOrderByIdAsync(int orderId);

    // Status-based Queries
    Task<List<OrdersTable>> GetDirectMangerPendingOrdersAsync();
    Task<List<OrdersTable>> GetAssistantManagerPendingOrdersAsync(AssistantManagerType assistantManagerId);
    Task<List<OrdersTable>> GetHRCoordinatorPendingOrdersAsync();
    Task<List<OrdersTable>> GetSupervisorsPendingOrdersAsync();
    Task<List<OrdersTable>> GetHRMangerPendingOrdersAsync();
    Task<List<OrdersTable>> GetOrdersByStatusAsync(OrderStatus status);
    Task<List<OrdersTable>> GetOrdersByStatusesAsync(OrderStatus[] statuses);

    // Additional methods for OrdersManagementService
    Task<List<OrdersTable>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<List<OrdersTable>> GetOrdersOlderThanAsync(DateTime cutoffDate);
    Task DeleteAsync(int orderId);
}
