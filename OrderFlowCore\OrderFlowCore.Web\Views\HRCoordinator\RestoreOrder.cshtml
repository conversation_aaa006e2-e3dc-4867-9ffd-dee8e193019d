@model OrderFlowCore.Web.ViewModels.HRCoordinatorViewModel
@{
    ViewData["Title"] = "استعادة الطلبات";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🔄 استعادة الطلبات</h2>
                <a href="@Url.Action("Index", "HRCoordinator")" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة الرئيسية
                </a>
            </div>

            <!-- Search and Filter Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">البحث والفلترة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="txtRestoreSearch" class="form-label"> البحث بستخدام رقم الطلب او اسم الموظف</label>
                            <input type="text" id="txtRestoreSearch" class="form-control" placeholder="ابحث عن طلب..." />
                        </div>
                        <div class="col-md-4">
                            <label for="ddlRestoreFilter" class="form-label">الفلتر</label>
                            <select id="ddlRestoreFilter" class="form-select">
                                <option value="all">الكل</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-primary w-100" onclick="HRCoordinator.searchRestorableOrders()">بحث</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Restorable Orders Selection -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">الطلبات القابلة للاستعادة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <label for="ddlRestorableOrders" class="form-label">اختر الطلب للاستعادة</label>
                            <select id="ddlRestorableOrders" class="form-select" onchange="HRCoordinator.loadRestoreOrderDetails(this.value)">
                                <option value="">-- اختر الطلب للاستعادة --</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Restore Details Panel -->
            <div id="RestoreDetailsPanel" style="display: none;" class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل الطلب المحدد</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>الحالة الحالية:</strong> <span id="lblRestoreCurrentStatus"></span><br>
                        <strong>تاريخ التحويل:</strong> <span id="lblRestoreTransferDate"></span><br>
                        <strong>المشرفين المعينين:</strong> <span id="lblRestoreAssignedSupervisors"></span>
                    </div>
                    
                    <!-- Order Details -->
                    <div id="restoreOrderDetailsContainer">
                        @await Html.PartialAsync("_OrderDetailsPartial")
                    </div>
                </div>
            </div>

            <!-- Restore Form -->
            <div id="RestoreFormSection" style="display: none;" class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">معلومات الاستعادة</h5>
                </div>
                <div class="card-body">
                    @using (Html.BeginForm("RestoreOrder", "HRCoordinator", FormMethod.Post))
                    {
                        @Html.AntiForgeryToken()
                        <input type="hidden" id="restoreOrderId" name="orderId" />
                        
                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="restoreNotes" class="form-label">ملاحظات الاستعادة</label>
                                <textarea name="restoreNotes" id="restoreNotes" class="form-control" rows="3" 
                                          placeholder="ملاحظات اختيارية حول سبب الاستعادة..."></textarea>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="button" class="btn btn-success" onclick="HRCoordinator.confirmRestore()">
                                <span class="action-icon">🔄</span>استعادة الطلب
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="HRCoordinator.clearRestoreForm()">
                                إلغاء
                            </button>
                        </div>
                    }
                </div>
            </div>

            <div id="messageContainer" class="mt-3"></div>
        </div>
    </div>
</div>

<!-- Confirm Restore Modal -->
<div class="modal fade" id="confirmRestoreModal" tabindex="-1" aria-labelledby="confirmRestoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmRestoreModalLabel">تأكيد استعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                <p>هل أنت متأكد من استعادة الطلب؟</p>
                <p class="text-muted">سيتم إعادة الطلب إلى حالة "قيد المعالجة" ويمكن إعادة توجيهه للمشرفين.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmRestoreModalBtn">نعم، استعادة</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/hrCoordinator.js"></script>
} 