using OfficeOpenXml;
using DocumentFormat.OpenXml.Bibliography;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;
using System.Web;
using System.IO;
using abozyad.Helpers;
using Microsoft.Extensions.Logging;



namespace abozyad
{


    public partial class WebForm3 : System.Web.UI.Page
    {

        protected void Page_Load(object sender, EventArgs e)
        {
            // إضافة التحقق من وجود مجلد Uploads في بداية الدالة
            if (!Directory.Exists(Server.MapPath("~/Uploads")))
            {
                Directory.CreateDirectory(Server.MapPath("~/Uploads"));
            }

            if (!IsPostBack)
            {
                LoadOrderNumbers(); // تحميل قائمة الطلبات
                PopulateStatusFilter();
                PopulateDepartmentFilter();
                BindGridView();
                LoadGeneralStatistics();
                LoadSupervisorStatisticsAndCompletionTime();
                LoadAssistantStatisticsAndCompletionTime();
                LoadDepartmentStatisticsAndCompletionTime();
                LoadCompletedSupervisorNames();
                BindGridWithMonthlyTotals();
                PopulateTransferTypeStatistics();
                LoadHRCoordinatorsStatistics();
                LoadHRManagerStatistics();


            }

            ddlDepartmentFilter.Enabled = true;
            if (Session["UserPermission"] == null)
            {
                Response.Redirect("AccessDenied.aspx");
                return;
            }

            string userPermission = Session["UserPermission"].ToString();
            bool isAuthorized = (userPermission == "مدير حسابات" ||
                                userPermission == "مدير الموارد البشرية");

            // تحديد ظهور الأزرار بناءً على الصلاحيات
            btnExportToExcel.Visible = isAuthorized;
            btnExportDetailedStatisticsToExcel.Visible = isAuthorized;
            btnGenerateReports.Visible = isAuthorized;
            btnDeleteOldOrders.Visible = isAuthorized;     // إضافة التحكم في ظهور زر الحذف
            ddlDeletePeriod.Visible = isAuthorized;        // إضافة التحكم في ظهور قائمة فترة الحذف
            pnlDateRange.Visible = isAuthorized && ddlDeletePeriod.SelectedValue == "-1";  // تم تحديث اسم اللوحة
            btnDeleteOrder.Visible = isAuthorized;
            ddlAllOrderNumbers.Visible = isAuthorized;

            if (!isAuthorized)
            {
                ddlDepartmentFilter.Enabled = false;
                SetDropDownListSelectedItem();
                BindGridView();
            }
        }



        private void PopulateTransferTypeStatistics()
        {
            Dictionary<string, int> transferTypeCount = new Dictionary<string, int>();
            StringBuilder detailsContent = new StringBuilder();
            StringBuilder summaryContent = new StringBuilder();
            int totalTransfers = 0;

            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            string query = "SELECT [نوع التحويل] FROM ordersTable WHERE [نوع التحويل] IS NOT NULL";

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                try
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string transferType = reader["نوع التحويل"].ToString().Trim();
                            if (!string.IsNullOrEmpty(transferType))
                            {
                                if (transferTypeCount.ContainsKey(transferType))
                                    transferTypeCount[transferType]++;
                                else
                                    transferTypeCount[transferType] = 1;
                                totalTransfers++;
                            }
                        }
                    }

                    foreach (var entry in transferTypeCount.OrderByDescending(x => x.Value))
                    {
                        detailsContent.Append($@"
                    <div class='department-row'>
                        <strong>{HttpUtility.HtmlEncode(entry.Key)}:</strong>
                        <span class='{GetValueClass(entry.Value)}'>العدد: {entry.Value}</span>
                    </div>");
                    }

                    summaryContent.Append($@"
                <div class='summary-stats'>
                    <strong class='completed'>إجمالي التحويلات: {totalTransfers}</strong> | 
                    <strong class='avg-completion-time'>عدد الأنواع: {transferTypeCount.Count}</strong>
                </div>");

                    TransferTypeSummary.Text = summaryContent.ToString();
                    trasnferTypeLiteral.Text = detailsContent.ToString();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إحصائيات التحويل: {ex.Message}");
                }
            }
        }


        private DataTable GetOrdersTableFromDatabase()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            DataTable dt = new DataTable();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                // Define the query to get the 'نوع التحويل' column from the orders table
                string query = "SELECT [نوع التحويل] FROM ordersTable";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();

                    // Use SqlDataAdapter to fill the DataTable with the result of the query
                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(dt);
                    }
                }
            }

            return dt;
        }



        // تقوم هذه الدالة بحساب إحصائيات المشرفين الخاصة بالطلبات تحت التنفيذ والطلبات المنجزة، بالإضافة إلى حساب متوسط زمن الإنجاز لكل مشرف.


        private void LoadSupervisorStatisticsAndCompletionTime()
        {
            StringBuilder mergedContent = new StringBuilder();
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"
WITH SupervisorData AS (
    SELECT 
        SupervisorName,
        [Status] = CASE 
            WHEN SupervisorValue LIKE N'%الطلب تحت التنفيذ%' THEN 'UnderExecution'
            WHEN SupervisorValue LIKE N'%اعتماد بواسطة%' THEN 'Completed'
            WHEN SupervisorValue LIKE N'%يتطلب إجراءات%' THEN 'NeedsAction'
            WHEN SupervisorValue LIKE N'%تم الإعادة من قبل%' THEN 'Returned'
            ELSE NULL
        END,
        CASE 
            WHEN SupervisorValue LIKE N'%اعتماد بواسطة%'
            THEN DATEDIFF(day, 
                TRY_CONVERT(date, LEFT(SupervisorValue, 10)), 
                TRY_CONVERT(date, RIGHT(SupervisorValue, 10))
            )
            ELSE NULL
        END AS DaysToComplete,
        SupervisorValue
    FROM ordersTable
    UNPIVOT (
        SupervisorValue FOR SupervisorName IN (
            [مشرف خدمات الموظفين],
            [مشرف إدارة تخطيط الموارد البشرية],
            [مشرف إدارة تقنية المعلومات],
            [مشرف مراقبة الدوام],
            [مشرف السجلات الطبية],
            [مشرف إدارة الرواتب والاستحقاقات],
            [مشرف إدارة القانونية والالتزام],
            [مشرف خدمات الموارد البشرية],
            [مشرف إدارة الإسكان],
            [مشرف قسم الملفات],
            [مشرف العيادات الخارجية],
            [مشرف التأمينات الاجتماعية],
            [مشرف وحدة مراقبة المخزون],
            [مشرف إدارة تنمية الإيرادات],
            [مشرف إدارة الأمن و السلامة],
            [مشرف الطب الاتصالي]
        )
    ) AS unpvt
    WHERE SupervisorValue IS NOT NULL
        AND (
            SupervisorValue LIKE N'%|%' 
            OR SupervisorValue LIKE N'%الطلب تحت التنفيذ%'
            OR SupervisorValue LIKE N'%يتطلب إجراءات%'
            OR SupervisorValue LIKE N'%تم الإعادة من قبل%'
        )
)
SELECT
    SupervisorName,
    SUM(CASE WHEN [Status] = 'UnderExecution' THEN 1 ELSE 0 END) AS UnderExecution,
    SUM(CASE WHEN [Status] = 'Completed' THEN 1 ELSE 0 END) AS Completed,
    SUM(CASE WHEN [Status] = 'NeedsAction' THEN 1 ELSE 0 END) AS NeedsAction,
    SUM(CASE WHEN [Status] = 'Returned' THEN 1 ELSE 0 END) AS Returned,
    AVG(CASE 
        WHEN [Status] = 'Completed' AND DaysToComplete IS NOT NULL
        THEN DaysToComplete
    END) AS AvgCompletionTime
FROM SupervisorData
GROUP BY SupervisorName
ORDER BY SupervisorName;";
                try
                {
                    con.Open();
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        int totalUnderExecution = 0;
                        int totalCompleted = 0;
                        int totalNeedsAction = 0;
                        int totalReturned = 0;
                        double totalCompletionTime = 0;
                        int supervisorCount = 0;

                        while (reader.Read())
                        {
                            string supervisorName = reader["SupervisorName"].ToString();
                            int underExecution = Convert.ToInt32(reader["UnderExecution"]);
                            int completed = Convert.ToInt32(reader["Completed"]);
                            int needsAction = Convert.ToInt32(reader["NeedsAction"]);
                            int returned = Convert.ToInt32(reader["Returned"]);
                            double avgCompletionTime = reader["AvgCompletionTime"] == DBNull.Value ?
                                0 : Convert.ToDouble(reader["AvgCompletionTime"]);

                            // تحديث الإحصائيات الإجمالية
                            totalUnderExecution += underExecution;
                            totalCompleted += completed;
                            totalNeedsAction += needsAction;
                            totalReturned += returned;
                            totalCompletionTime += avgCompletionTime;
                            supervisorCount++;

                            // إضافة HTML لكل مشرف
                            mergedContent.Append($@"
    <div class='department-row'>
        <strong>{HttpUtility.HtmlEncode(supervisorName)}:</strong>
        <span class='{GetValueClass(underExecution)}'>تحت التنفيذ: {underExecution}</span> | 
        <span class='{GetValueClass(completed)}'>منجز: {completed}</span> | 
        <span class='text-warning'>يتطلب إجراءات: {needsAction}</span> | 
        <span class='text-danger'>معاد: {returned}</span> | 
        <span class='avg-completion-time'>متوسط زمن الإنجاز: {avgCompletionTime:F2} أيام</span>
    </div>");
                        }

                        // إعداد الإحصائيات الإجمالية
                        if (supervisorCount > 0)
                        {
                            double overallAvgCompletionTime = totalCompletionTime / supervisorCount;

                            SupervisorSummaryStatistics.Text = $@"
    <div class='summary-stats'>
        <strong class='under-execution'>تحت التنفيذ: {totalUnderExecution}</strong> | 
        <strong class='completed'>منجز: {totalCompleted}</strong> | 
        <strong class='text-warning'>يتطلب إجراءات: {totalNeedsAction}</strong> | 
        <strong class='text-danger'>معاد: {totalReturned}</strong> |
        <strong class='avg-completion-time'>متوسط زمن الإنجاز: {overallAvgCompletionTime:F2} أيام</strong>
    </div>";
                        }

                        SupervisorStatistics.Text = mergedContent.ToString();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إحصائيات المشرفين: {ex.Message}");
                    SupervisorStatistics.Text = $"<div class='alert alert-danger'>حدث خطأ أثناء تحميل البيانات: {ex.Message}</div>";
                    SupervisorSummaryStatistics.Text = string.Empty;
                }
            }
        }




        private void LoadHRManagerStatistics()
        {
            StringBuilder mergedContent = new StringBuilder();
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            // أولاً: حساب إجمالي الطلبات تحت التنفيذ
            int totalUnderExecution = 0;
            using (SqlConnection countCon = new SqlConnection(connectionString))
            {
                string countQuery = @"
            SELECT COUNT(*) 
            FROM ordersTable 
            WHERE [حالة الطلب] = '(D)'";

                using (SqlCommand countCmd = new SqlCommand(countQuery, countCon))
                {
                    countCon.Open();
                    totalUnderExecution = (int)countCmd.ExecuteScalar();
                }
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"
WITH ManagerData AS (
    SELECT 
        CASE
            WHEN [مدير الموارد البشرية] LIKE N'%تم الإعادة بواسطة%' OR 
                 [مدير الموارد البشرية] LIKE N'%تم الإلغاء بواسطة%' OR 
                 [مدير الموارد البشرية] LIKE N'%تم تغيير الحالة%'
            THEN RTRIM(LTRIM(SUBSTRING(
                [مدير الموارد البشرية],
                CHARINDEX(N'بواسطة', [مدير الموارد البشرية]) + LEN(N'بواسطة'),
                LEN([مدير الموارد البشرية])
            )))
            WHEN [مدير الموارد البشرية] IS NOT NULL
            THEN RTRIM(LTRIM(
                SUBSTRING(
                    [مدير الموارد البشرية],
                    1,
                    CASE 
                        WHEN CHARINDEX(' 14', [مدير الموارد البشرية]) > 0 
                        THEN CHARINDEX(' 14', [مدير الموارد البشرية])
                        ELSE LEN([مدير الموارد البشرية])
                    END
                )
            ))
        END as ManagerName,
        CASE 
            WHEN [مدير الموارد البشرية] LIKE N'%تم تغيير الحالة%' THEN N'تغيير الحالة'
            WHEN [حالة الطلب] = 'مقبول' OR [مدير الموارد البشرية] LIKE N'%اعتماد بواسطة%' THEN N'منجز'
            WHEN [حالة الطلب] = N'أُعيد بواسطة المدير' OR 
                 [مدير الموارد البشرية] LIKE N'%تم الإعادة بواسطة%' OR
                 [حالة الطلب] = N'معاد' THEN N'معاد'
            WHEN [حالة الطلب] = N'تم الإلغاء من قبل المدير' OR 
                 [مدير الموارد البشرية] LIKE N'%تم الإلغاء بواسطة%' OR
                 [حالة الطلب] = N'ملغي' THEN N'ملغي'
            ELSE NULL
        END as Status
    FROM ordersTable
    WHERE [مدير الموارد البشرية] IS NOT NULL
)
SELECT
    TRIM(ManagerName) as UnifiedManagerName,
    ISNULL(SUM(CASE WHEN Status = N'منجز' THEN 1 ELSE 0 END), 0) AS Completed,
    ISNULL(SUM(CASE WHEN Status = N'تغيير الحالة' THEN 1 ELSE 0 END), 0) AS StatusChanged,
    ISNULL(SUM(CASE WHEN Status = N'معاد' THEN 1 ELSE 0 END), 0) AS Returned,
    ISNULL(SUM(CASE WHEN Status = N'ملغي' THEN 1 ELSE 0 END), 0) AS Cancelled
FROM ManagerData
WHERE 
    ManagerName IS NOT NULL 
    AND LEN(TRIM(ManagerName)) > 0
    AND TRIM(ManagerName) != N'المدير'
GROUP BY TRIM(ManagerName)
ORDER BY UnifiedManagerName;";

                try
                {
                    con.Open();
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        int totalCompleted = 0;
                        int totalStatusChanged = 0;
                        int totalReturned = 0;
                        int totalCancelled = 0;
                        int managerCount = 0;

                        while (reader.Read())
                        {
                            string managerName = reader["UnifiedManagerName"].ToString();
                            int completed = Convert.ToInt32(reader["Completed"]);
                            int statusChanged = Convert.ToInt32(reader["StatusChanged"]);
                            int returned = Convert.ToInt32(reader["Returned"]);
                            int cancelled = Convert.ToInt32(reader["Cancelled"]);

                            totalCompleted += completed;
                            totalStatusChanged += statusChanged;
                            totalReturned += returned;
                            totalCancelled += cancelled;
                            managerCount++;

                            // عرض إحصائيات كل مدير
                            mergedContent.Append($@"
<div class='manager-row mb-4'>
    <div class='manager-name mb-2'><strong>{HttpUtility.HtmlEncode(managerName)}</strong></div>
    <div class='operations-list'>
        <div class='operation-item'>
            <div class='stats-line'> 
                <strong class='text-success'>منجز: {completed}</strong> | 
                <strong class='text-warning'>تغيير الحالة: {statusChanged}</strong> | 
                <strong class='text-info'>معاد: {returned}</strong> | 
                <strong class='text-danger'>ملغي: {cancelled}</strong>
            </div>
        </div>
    </div>
</div>");
                        }

                        if (managerCount > 0)
                        {
                            // عرض الإحصائيات الإجمالية مع إجمالي الطلبات تحت التنفيذ
                            string summaryContent = $@"
<div class='summary-stats'>
    <div class='mb-2'>
        <strong class='text-primary'>تحت التنفيذ: {totalUnderExecution}</strong> | 
        <strong class='text-success'>منجز: {totalCompleted}</strong> | 
        <strong class='text-warning'>تغيير الحالة: {totalStatusChanged}</strong> | 
        <strong class='text-info'>معاد: {totalReturned}</strong> | 
        <strong class='text-danger'>ملغي: {totalCancelled}</strong>
    </div>
</div>";
                            HRManagerSummaryStatistics.Text = summaryContent;
                            HRManagerStatistics.Text = mergedContent.ToString();
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}\n{ex.StackTrace}");
                    HRManagerSummaryStatistics.Text = "<div class='alert alert-danger'>حدث خطأ أثناء تحميل البيانات</div>";
                    HRManagerStatistics.Text = string.Empty;
                }
            }
        }




        // تعديل دالة احصائيات المساعدين

        private class AssistantStats
        {
            public int UnderExecution { get; set; }
            public int Completed { get; set; }
            public int Returned { get; set; } //  المعادة
            public int Cancelled { get; set; } //  الملغية
            public double TotalCompletionTime { get; set; }
            public int CompletedRequestsCount { get; set; }
            public double AverageCompletionTime =>
                CompletedRequestsCount > 0 ? TotalCompletionTime / CompletedRequestsCount : 0;
        }
        private readonly string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        private AssistantStats CalculateAssistantStats(string assistant)
        {
            var stats = new AssistantStats();

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                // استعلام محدث لحساب الإحصائيات لكل مساعد بما يشمل المعاد والملغي
                string query = @"
SELECT 
    COUNT(CASE 
        WHEN o.[تم التأكيد/الإلغاء من قبل المشرف] LIKE N'%الطلب تحت التنفيذ%' 
        THEN 1 END) as UnderExecution,
    COUNT(CASE 
        WHEN o.[تم التأكيد/الإلغاء من قبل المشرف] LIKE N'%اعتماد بواسطة%' 
        THEN 1 END) as Completed,
    COUNT(CASE 
        WHEN o.[تم التأكيد/الإلغاء من قبل المشرف] LIKE N'%تمت الإعادة من مساعد المدير%' 
        THEN 1 END) as Returned,
    COUNT(CASE 
        WHEN o.[حالة الطلب] = N'تم الإلغاء من قبل المشرف' 
        THEN 1 END) as Cancelled,
    AVG(CASE 
        WHEN o.[تم التأكيد/الإلغاء من قبل المشرف] LIKE N'%اعتماد بواسطة%' 
        THEN DATEDIFF(day, 
            TRY_CONVERT(date, LEFT(o.[تم التأكيد/الإلغاء من قبل المشرف], 10)), 
            TRY_CONVERT(date, RIGHT(o.[تم التأكيد/الإلغاء من قبل المشرف], 10)))
        END) as AvgCompletionTime
FROM ordersTable o
JOIN Departments d ON o.[القسم] = d.[الأقسام]
WHERE d.AssistantManagerID = @AssistantID";

                string assistantId = "";
                switch (assistant)
                {
                    case "مساعد المدير للخدمات الطبية":
                        assistantId = "A1";
                        break;
                    case "مساعد المدير لخدمات التمريض":
                        assistantId = "A2";
                        break;
                    case "مساعد المدير للخدمات الإدارية والتشغيل":
                        assistantId = "A3";
                        break;
                    case "مساعد المدير للموارد البشرية":
                        assistantId = "A4";
                        break;
                }

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@AssistantID", assistantId);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            stats.UnderExecution = reader.IsDBNull(0) ? 0 : Convert.ToInt32(reader[0]);
                            stats.Completed = reader.IsDBNull(1) ? 0 : Convert.ToInt32(reader[1]);
                            stats.Returned = reader.IsDBNull(2) ? 0 : Convert.ToInt32(reader[2]);  // قراءة قيمة المعاد
                            stats.Cancelled = reader.IsDBNull(3) ? 0 : Convert.ToInt32(reader[3]); // قراءة قيمة الملغي
                            stats.TotalCompletionTime = reader.IsDBNull(4) ? 0 : Convert.ToDouble(reader[4]);
                            stats.CompletedRequestsCount = stats.Completed;
                        }
                    }
                }
            }

            return stats;
        }
        private void LoadAssistantStatisticsAndCompletionTime()
        {
            if (GridView1.Rows.Count == 0 || GridView1.HeaderRow == null)
            {
                AssistantSummaryStatistics.Text = "<div class='no-data'>لا توجد طلبات للعرض حاليًا.</div>";
                AssistantStatistics.Text = string.Empty;
                return;
            }

            StringBuilder mergedContent = new StringBuilder();

            string[] assistants = new string[] {
        "مساعد المدير للخدمات الطبية",
        "مساعد المدير لخدمات التمريض",
        "مساعد المدير للخدمات الإدارية والتشغيل",
        "مساعد المدير للموارد البشرية"
    };

            var totals = new
            {
                UnderExecution = 0,
                Completed = 0,
                Returned = 0,     // إضافة المعاد إلى الإجماليات
                Cancelled = 0,    // إضافة الملغي إلى الإجماليات
                CompletionTime = 0.0,
                CompletedRequests = 0
            };

            foreach (string assistant in assistants)
            {
                var stats = CalculateAssistantStats(assistant);

                totals = new
                {
                    UnderExecution = totals.UnderExecution + stats.UnderExecution,
                    Completed = totals.Completed + stats.Completed,
                    Returned = totals.Returned + stats.Returned,         // إضافة قيمة المعاد للإجمالي
                    Cancelled = totals.Cancelled + stats.Cancelled,      // إضافة قيمة الملغي للإجمالي
                    CompletionTime = totals.CompletionTime + stats.TotalCompletionTime,
                    CompletedRequests = totals.CompletedRequests + stats.CompletedRequestsCount
                };

                mergedContent.Append($@"
<div class='department-row'>
    <strong>{HttpUtility.HtmlEncode(assistant)}:</strong>
    <span class='{GetValueClass(stats.UnderExecution)}'>تحت التنفيذ: {stats.UnderExecution}</span> | 
    <span class='{GetValueClass(stats.Completed)}'>منجز: {stats.Completed}</span> | 
    <span class='text-info'>معاد: {stats.Returned}</span> | 
    <span class='text-danger'>ملغي: {stats.Cancelled}</span> | 
    <span class='avg-completion-time'>متوسط زمن الإنجاز: {stats.AverageCompletionTime:F2} أيام</span>
</div>");
            }

            double overallAvgCompletionTime = totals.CompletedRequests > 0
                ? totals.CompletionTime / totals.CompletedRequests
                : 0;

            var summaryContent = $@"
<div class='summary-stats'>
    <strong class='under-execution'>تحت التنفيذ: {totals.UnderExecution}</strong> | 
    <strong class='completed'>منجز: {totals.Completed}</strong> | 
    <strong class='text-info'>معاد: {totals.Returned}</strong> |
    <strong class='text-danger'>ملغي: {totals.Cancelled}</strong> |
    <strong class='avg-completion-time'>متوسط زمن الإنجاز: {overallAvgCompletionTime:F2} أيام</strong>
</div>";

            AssistantSummaryStatistics.Text = summaryContent;
            AssistantStatistics.Text = mergedContent.ToString();
        }



        private int FindAssistantColumn(string assistant)
        {
            for (int i = 0; i < GridView1.HeaderRow.Cells.Count; i++)
            {
                if (GridView1.HeaderRow.Cells[i].Text.Contains(assistant))
                {
                    return i;
                }
            }
            return -1;
        }

        private void ProcessCompletionTime(string cellValue, AssistantStats stats)
        {
            string[] parts = cellValue.Split('|');
            if (parts.Length > 1)
            {
                string dateReceivedStr = parts[0].Trim();
                string[] signedParts = parts[1].Split(' ');
                string dateSignedStr = signedParts[signedParts.Length - 1].Trim();

                if (DateTime.TryParse(dateReceivedStr, out DateTime dateReceived) &&
                    DateTime.TryParse(dateSignedStr, out DateTime dateSigned))
                {
                    stats.TotalCompletionTime += (dateSigned - dateReceived).TotalDays;
                    stats.CompletedRequestsCount++;
                }
            }
        }





        private void LoadDepartmentStatisticsAndCompletionTime()
        {
            StringBuilder mergedContent = new StringBuilder();
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"
SELECT 
    [القسم],
    COUNT(CASE 
        WHEN [تم التأكيد/الإلغاء من مدير القسم] LIKE N'%تم التحويل إلى مدير القسم%' 
        THEN 1 END) AS UnderExecution,
    COUNT(CASE 
        WHEN [تم التأكيد/الإلغاء من مدير القسم] LIKE N'%اعتماد بواسطة%' 
        THEN 1 END) AS Completed,
    COUNT(CASE 
        WHEN [حالة الطلب] = N'تم الإلغاء من مدير القسم'
        THEN 1 END) AS Cancelled,
    AVG(CASE 
        WHEN [تم التأكيد/الإلغاء من مدير القسم] LIKE N'%اعتماد بواسطة%' 
        THEN DATEDIFF(day, 
            TRY_CONVERT(date, LEFT([تم التأكيد/الإلغاء من مدير القسم], 10)), 
            TRY_CONVERT(date, RIGHT([تم التأكيد/الإلغاء من مدير القسم], 10))
        )
        END) AS CompletionTime
FROM ordersTable 
WHERE [القسم] IS NOT NULL
GROUP BY [القسم]
ORDER BY [القسم]";

                try
                {
                    con.Open();
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            int totalUnderExecution = 0;
                            int totalCompleted = 0;
                            int totalCancelled = 0;
                            double totalCompletionTime = 0;
                            int departmentCount = 0;

                            while (reader.Read())
                            {
                                string department = reader["القسم"].ToString();
                                int underExecution = Convert.ToInt32(reader["UnderExecution"]);
                                int completed = Convert.ToInt32(reader["Completed"]);
                                int cancelled = Convert.ToInt32(reader["Cancelled"]);
                                double avgCompletionTime = reader["CompletionTime"] == DBNull.Value ?
                                    0 : Convert.ToDouble(reader["CompletionTime"]);

                                // تحديث الإحصائيات الإجمالية
                                totalUnderExecution += underExecution;
                                totalCompleted += completed;
                                totalCancelled += cancelled;
                                totalCompletionTime += avgCompletionTime;
                                departmentCount++;

                                // إضافة HTML لكل قسم
                                mergedContent.Append($@"
<div class='department-row'>
    <strong>{department}:</strong>
    <span class='text-primary'>تحت التنفيذ: {underExecution}</span> | 
    <span class='text-success'>منجز: {completed}</span> | 
    <span class='text-danger'>ملغي: {cancelled}</span> | 
    <span class='text-muted'>متوسط زمن الإنجاز: {avgCompletionTime:F2} أيام</span>
</div>");
                            }

                            // إعداد الإحصائيات الإجمالية
                            if (departmentCount > 0)
                            {
                                double overallAvgCompletionTime = totalCompletionTime / departmentCount;

                                DepartmentSummaryStatistics.Text = $@"
<div class='summary-stats'>
    <strong class='text-primary'>تحت التنفيذ: {totalUnderExecution}</strong> | 
    <strong class='text-success'>منجز: {totalCompleted}</strong> | 
    <strong class='text-danger'>ملغي: {totalCancelled}</strong> | 
    <strong class='text-muted'>متوسط زمن الإنجاز: {overallAvgCompletionTime:F2} أيام</strong>
</div>";
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل إحصائيات الأقسام: {ex.Message}");
                }
            }

            DepartmentStatisticsColumn1.Text = mergedContent.ToString();
        }



        // دالة مساعدة لتحديد فئة CSS المناسبة حسب القيمة
        private string GetValueClass(int value)
        {
            if (value <= 0) return "value-0";
            if (value <= 3) return "value-1-3";
            if (value <= 7) return "value-4-7";
            if (value <= 10) return "value-8-10";
            return "value-more-than-10";
        }

        // تقوم هذه الدالة بحساب عدد الطلبات المنجزة لكل منسق موارد بشرية وعرض الأسماء مع عدد الطلبات المنجزة.
        private void LoadHRCoordinatorsStatistics()
        {
            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                StringBuilder mergedContent = new StringBuilder();

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
WITH CoordinatorData AS (
    SELECT 
        CASE
            WHEN [تم التحويل/الإلغاء من قبل المنسق] IS NULL OR TRIM([تم التحويل/الإلغاء من قبل المنسق]) = ''
            THEN N'الطلبات القادمة'
            WHEN [تم التحويل/الإلغاء من قبل المنسق] LIKE N'%بواسطة%'
            THEN REPLACE(
                    SUBSTRING([تم التحويل/الإلغاء من قبل المنسق], 
                            CHARINDEX(N'بواسطة', [تم التحويل/الإلغاء من قبل المنسق]) + LEN(N'بواسطة') + 1, 
                            LEN([تم التحويل/الإلغاء من قبل المنسق])), 
                    N'بواسطة', 
                    N''
                )
            ELSE [تم التحويل/الإلغاء من قبل المنسق]
        END as CleanedCoordinatorName,
        CASE
            WHEN [تم التحويل/الإلغاء من قبل المنسق] LIKE N'%تمت استعادة الطلب%'
            THEN N'تمت استعادة الطلب'
            WHEN [تم التحويل/الإلغاء من قبل المنسق] LIKE N'%تم الإعادة%'
            THEN N'تم الإعادة'
            ELSE N'منجز'
        END as ActionType,
        [حالة الطلب] as OrderStatus,
        [نوع التحويل] as TransferType
    FROM ordersTable
    WHERE [تم التحويل/الإلغاء من قبل المنسق] IS NOT NULL
)
SELECT 
    TRIM(CleanedCoordinatorName) as UnifiedCoordinatorName,
    SUM(CASE WHEN ActionType = N'تمت استعادة الطلب' THEN 1 ELSE 0 END) as Restored,
    SUM(CASE WHEN ActionType = N'تم الإعادة' THEN 1 ELSE 0 END) as Reassigned,
    SUM(CASE WHEN ActionType = N'منجز' THEN 1 ELSE 0 END) as Completed,
    SUM(CASE WHEN OrderStatus = N'يتطلب إجراءات' THEN 1 ELSE 0 END) as NeedsAction,
    SUM(CASE WHEN OrderStatus = N'تم الإلغاء من قبل المنسق' THEN 1 ELSE 0 END) as Cancelled,
    SUM(CASE WHEN TransferType LIKE N'%تلقائي%' THEN 1 ELSE 0 END) as AutomaticTransfers,
    SUM(CASE WHEN TransferType NOT LIKE N'%تلقائي%' AND TransferType IS NOT NULL THEN 1 END) as ManualTransfers
FROM CoordinatorData
GROUP BY TRIM(CleanedCoordinatorName)
ORDER BY TRIM(CleanedCoordinatorName);";

                    con.Open();
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        int totalAuto = 0;
                        int totalManual = 0;
                        int totalNeedsAction = 0;
                        int totalCancelled = 0;
                        int totalRestored = 0;
                        int totalReassigned = 0;
                        int totalCompleted = 0;

                        var coordinatorStats = new Dictionary<string, CoordinatorStats>();

                        while (reader.Read())
                        {
                            string coordinatorName = reader["UnifiedCoordinatorName"].ToString().Trim();
                            int restored = Convert.ToInt32(reader["Restored"]);
                            int reassigned = Convert.ToInt32(reader["Reassigned"]);
                            int completed = Convert.ToInt32(reader["Completed"]);
                            int needsAction = Convert.ToInt32(reader["NeedsAction"]);
                            int cancelled = Convert.ToInt32(reader["Cancelled"]);
                            int auto = Convert.ToInt32(reader["AutomaticTransfers"]);
                            int manual = Convert.ToInt32(reader["ManualTransfers"]);

                            if (!coordinatorStats.ContainsKey(coordinatorName))
                            {
                                coordinatorStats[coordinatorName] = new CoordinatorStats();
                            }

                            var stats = coordinatorStats[coordinatorName];
                            stats.Restored += restored;
                            stats.Reassigned += reassigned;
                            stats.Completed += completed;
                            stats.NeedsAction += needsAction;
                            stats.Cancelled += cancelled;
                            stats.AutomaticTransfers += auto;
                            stats.ManualTransfers += manual;

                            totalAuto += auto;
                            totalManual += manual;
                            totalNeedsAction += needsAction;
                            totalCancelled += cancelled;
                            totalRestored += restored;
                            totalReassigned += reassigned;
                            totalCompleted += completed;
                        }

                        foreach (var coordinator in coordinatorStats)
                        {
                            mergedContent.Append($@"
<div class='coordinator-row mb-4'>
    <div class='coordinator-name mb-2'><strong>{HttpUtility.HtmlEncode(coordinator.Key)}</strong></div>
    <div class='operations-list'>
        <div class='operation-item'>
            <div class='stats-line'>
<strong class='text-primary'>منجز: {coordinator.Value.Completed}</strong> | 
<strong class='text-success'>استعادة الطلب: {coordinator.Value.Restored}</strong> | 
<strong class='text-info'>الإعادة: {coordinator.Value.Reassigned}</strong> | 
<strong class='text-warning'>يتطلب إجراءات: {coordinator.Value.NeedsAction}</strong> | 
<strong class='text-danger'>ملغي: {coordinator.Value.Cancelled}</strong>
            </div>
            <div class='transfer-line mt-2'>
                <span class='text-primary'>تلقائي: {coordinator.Value.AutomaticTransfers}</span> | 
                <span class='text-success'>يدوي: {coordinator.Value.ManualTransfers}</span>
            </div>
        </div>
    </div>
</div>");
                        }

                        int totalAll = totalCompleted + totalRestored + totalReassigned;

                        string summaryContent = $@"
<div class='summary-stats'>
    <div class='mb-2'>
<strong class='text-primary'>إجمالي المنجز: {totalAll}</strong> | 
<strong class='text-success'>استعادة الطلب: {totalRestored}</strong> | 
<strong class='text-info'>إعادة: {totalReassigned}</strong> | 
<strong class='text-warning'>يتطلب إجراءات: {totalNeedsAction}</strong> | 
<strong class='text-danger'>ملغي: {totalCancelled}</strong>
    </div>
    <div class='transfer-summary mt-2'>
        <span class='text-primary'>تلقائي: {totalAuto}</span> | 
        <span class='text-success'>يدوي: {totalManual}</span>
    </div>
</div>";

                        CoordinatorSummaryStatistics.Text = summaryContent;
                        CoordinatorStatisticsNames.Text = mergedContent.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadHRCoordinatorsStatistics: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                CoordinatorSummaryStatistics.Text = "<div class='alert alert-danger'>حدث خطأ أثناء تحميل الإحصائيات</div>";
                CoordinatorStatisticsNames.Text = string.Empty;
            }
        }

        public class CoordinatorStats
        {
            public int Completed { get; set; }
            public int Restored { get; set; }
            public int Reassigned { get; set; }
            public int NeedsAction { get; set; }
            public int Cancelled { get; set; }
            public int AutomaticTransfers { get; set; }
            public int ManualTransfers { get; set; }
        }

        private string ExtractCoordinatorName(string actionText)
        {
            if (string.IsNullOrEmpty(actionText) || actionText.Trim() == "&nbsp;")
            {
                return "الطلبات القادمة";
            }

            // تنظيف وتحضير النص
            string cleanText = HttpUtility.HtmlDecode(actionText).Trim();
            cleanText = cleanText.Replace("&nbsp;", " ").Trim();

            // البحث عن كلمة "اعتماد بواسطة" أو "تم التحويل بواسطة"
            if (cleanText.Contains("اعتماد بواسطة"))
            {
                cleanText = cleanText.Split(new[] { "اعتماد بواسطة" }, StringSplitOptions.RemoveEmptyEntries).LastOrDefault()?.Trim() ?? string.Empty;
            }
            else if (cleanText.Contains("تم التحويل بواسطة"))
            {
                cleanText = cleanText.Split(new[] { "تم التحويل بواسطة" }, StringSplitOptions.RemoveEmptyEntries).LastOrDefault()?.Trim() ?? string.Empty;
            }

            // إزالة التاريخ والرموز الإضافية
            cleanText = System.Text.RegularExpressions.Regex.Replace(cleanText, @"\d{4}-\d{2}-\d{2}", "");
            cleanText = cleanText.Replace("|", "").Trim();

            // إذا كان النص فارغاً بعد كل المعالجة، نرجع "الطلبات القادمة"
            return string.IsNullOrWhiteSpace(cleanText) ? "الطلبات القادمة" : cleanText;
        }







        // تقوم هذه الدالة بحساب عدد الطلبات المنجزة لكل مشرف وعرض الأسماء مع عدد الطلبات المنجزة.
        // 1. يتم التكرار على الصفوف والأعمدة في GridView للتحقق من الأعمدة الخاصة بالمشرفين (من العمود 16 إلى 32).
        // 2. إذا كانت الخلية تحتوي على "اعتماد بواسطة"، يتم استخراج اسم المشرف من الخلية بعد إزالة التاريخ.
        // 3. يتم تخزين عدد الطلبات المنجزة لكل مشرف في قاموس (Dictionary) لاحتساب عدد الطلبات لكل مشرف.
        // 4. بعد جمع البيانات، يتم تقسيم المشرفين بالتساوي بين عمودين للعرض (SupervisorStatisticsNames ).
        // 5. يتم عرض النتائج النهائية في العنصرين (SupervisorStatisticsNames ).

        private void LoadCompletedSupervisorNames()
        {
            Dictionary<string, int> supervisorCompletionCount = new Dictionary<string, int>();
            int totalCompleted = 0;

            // جمع البيانات
            foreach (GridViewRow row in GridView1.Rows)
            {
                for (int i = 16; i <= 32; i++)
                {
                    if (i < row.Cells.Count && row.Cells[i] != null)
                    {
                        string cellValue = row.Cells[i].Text?.Trim() ?? string.Empty;
                        if (cellValue.Contains("اعتماد بواسطة"))
                        {
                            string supervisorInfo = cellValue.Replace("اعتماد بواسطة", "").Trim();
                            supervisorInfo = supervisorInfo.Replace("|", "").Trim();
                            string supervisorName = System.Text.RegularExpressions.Regex.Replace(supervisorInfo, @"\d{4}-\d{2}-\d{2}", "").Trim();

                            if (!string.IsNullOrEmpty(supervisorName))
                            {
                                if (supervisorCompletionCount.ContainsKey(supervisorName))
                                {
                                    supervisorCompletionCount[supervisorName]++;
                                }
                                else
                                {
                                    supervisorCompletionCount[supervisorName] = 1;
                                }
                                totalCompleted++;
                            }
                        }
                    }
                }
            }

            // إنشاء محتوى التفاصيل
            StringBuilder detailsContent = new StringBuilder();
            foreach (var entry in supervisorCompletionCount.OrderByDescending(x => x.Value))
            {
                detailsContent.Append($@"
            <div class='department-row'>
                <strong>{HttpUtility.HtmlEncode(entry.Key)}:</strong>
                <span class='{GetValueClass(entry.Value)}'>منجز: {entry.Value}</span>
            </div>");
            }

            // إنشاء ملخص الإحصائيات
            string summaryContent = $@"
        <div class='summary-stats'>
            <strong class='completed'>إجمالي الطلبات المنجزة: {totalCompleted}</strong> | 
            <strong class='avg-completion-time'>عدد المشرفين: {supervisorCompletionCount.Count}</strong>
        </div>";

            // تحديث الواجهة
            CompletedTasksSummary.Text = summaryContent;
            SupervisorStatisticsNames.Text = detailsContent.ToString();
        }








        // تقوم هذه الدالة بمعالجة حدث النقر على زر "توليد التقارير".
        // 1. عند نقر المستخدم على الزر، يتم استدعاء الدالة GenerateAllReports.
        // 2. دالة GenerateAllReports مسؤولة عن توليد جميع التقارير اللازمة (حسب الحاجة).

        protected void GenerateReportsButton_Click(object sender, EventArgs e)
        {
            GenerateAllReports(); // استدعاء الدالة لتوليد جميع التقارير
        }



        // تقوم هذه الدالة بتحميل الإحصائيات العامة للطلبات من قاعدة البيانات.
        // 1. تتصل بقاعدة البيانات وتقوم بتنفيذ استعلام SQL لجلب:
        //    - العدد الإجمالي للطلبات.
        //    - عدد الطلبات المقبولة (CompletedRequests).
        //    - عدد الطلبات المعلقة حسب الحالات المختلفة (PendingRequests).
        //    - متوسط زمن الإنجاز للطلبات المقبولة (AvgCompletionTime).
        //    - عدد الطلبات لكل حالة (A, B, C, D).
        // 2. تعرض النتائج في الحقول المناسبة (Labels).
        // 3. إذا لم يتم العثور على بيانات، يتم عرض رسالة خطأ.
        // 4. في حالة حدوث خطأ في الاتصال بقاعدة البيانات، يتم عرض رسالة خطأ.

        private void LoadGeneralStatistics()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    string combinedQuery = @"
                SELECT 
                    COUNT(*) AS TotalRequests,
                    SUM(CASE WHEN [حالة الطلب] = 'مقبول' THEN 1 ELSE 0 END) AS CompletedRequests,
                    SUM(CASE WHEN [حالة الطلب] NOT IN ('مقبول', 'مرفوض') THEN 1 ELSE 0 END) AS PendingRequests,
                    AVG(CASE WHEN [حالة الطلب] = 'مقبول' THEN DATEDIFF(day, [تاريخ الطلب], GETDATE()) ELSE NULL END) AS AvgCompletionTime,
                    
                    -- عدد الطلبات حسب المرحلة
                    SUM(CASE WHEN [حالة الطلب] = '(DM)' THEN 1 ELSE 0 END) AS StatusDMCount,
                    SUM(CASE WHEN [حالة الطلب] = '(A1)' THEN 1 ELSE 0 END) AS StatusA1Count,
                    SUM(CASE WHEN [حالة الطلب] = '(A2)' THEN 1 ELSE 0 END) AS StatusA2Count,
                    SUM(CASE WHEN [حالة الطلب] = '(A3)' THEN 1 ELSE 0 END) AS StatusA3Count,
                    SUM(CASE WHEN [حالة الطلب] = '(A4)' THEN 1 ELSE 0 END) AS StatusA4Count,
                    SUM(CASE WHEN [حالة الطلب] = '(B)' OR [حالة الطلب] LIKE '%تم الإعادة%' THEN 1 ELSE 0 END) AS StatusBCount,
                    SUM(CASE WHEN [حالة الطلب] = '(C)' THEN 1 ELSE 0 END) AS StatusCCount,
                    SUM(CASE WHEN [حالة الطلب] = '(D)' THEN 1 ELSE 0 END) AS StatusDCount
                FROM ordersTable;";

                    using (SqlCommand cmd = new SqlCommand(combinedQuery, connection))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // الإحصائيات العامة
                                lblTotalRequests.Text = reader["TotalRequests"].ToString();
                                lblCompletedRequests.Text = reader["CompletedRequests"].ToString();
                                lblPendingRequests.Text = reader["PendingRequests"].ToString();

                                // تنسيق متوسط وقت الإكمال
                                var avgTime = reader["AvgCompletionTime"];
                                lblAvgTimeToComplete.Text = avgTime != DBNull.Value ?
                                    $"{Math.Round(Convert.ToDouble(avgTime), 1)} يوم" : "غير متوفر";

                                // عدد الطلبات في كل مرحلة
                                lblRequestsDM.Text = FormatNumberWithColor(GetIntValue(reader, "StatusDMCount"));
                                lblRequestsA1.Text = FormatNumberWithColor(GetIntValue(reader, "StatusA1Count"));
                                lblRequestsA2.Text = FormatNumberWithColor(GetIntValue(reader, "StatusA2Count"));
                                lblRequestsA3.Text = FormatNumberWithColor(GetIntValue(reader, "StatusA3Count"));
                                lblRequestsA4.Text = FormatNumberWithColor(GetIntValue(reader, "StatusA4Count"));
                                lblRequestsB.Text = FormatNumberWithColor(GetIntValue(reader, "StatusBCount"));
                                lblRequestsC.Text = FormatNumberWithColor(GetIntValue(reader, "StatusCCount"));
                                lblRequestsD.Text = FormatNumberWithColor(GetIntValue(reader, "StatusDCount"));
                            }
                            else
                            {
                                lblErrorMessage.Text = "لا توجد بيانات لعرضها.";
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lblErrorMessage.Text = "حدث خطأ أثناء تحميل الإحصائيات. الرجاء المحاولة لاحقاً.";
                LogError(ex);
            }
        }
        private void LogError(Exception ex)
        {
            // تسجيل الخطأ في ملف نصي أو نظام تسجيل (مثل قاعدة بيانات أو Windows Event Log)
            string logPath = Server.MapPath("~/Logs/ErrorLog.txt");
            string errorDetails = $"{DateTime.Now}: {ex.Message}{Environment.NewLine}{ex.StackTrace}{Environment.NewLine}";

            try
            {
                File.AppendAllText(logPath, errorDetails);
            }
            catch
            {
                // إذا فشل تسجيل الخطأ، يمكنك إرسال بريد إلكتروني أو إظهار رسالة فقط
            }
        }

        // دالة مساعدة لتحويل القيم من DBNull إلى int
        private int GetIntValue(SqlDataReader reader, string columnName)
        {
            return reader[columnName] != DBNull.Value ? Convert.ToInt32(reader[columnName]) : 0;
        }

        // دالة لتنسيق الرقم مع اللون المناسب
        private string FormatNumberWithColor(int value)
        {
            if (value == 0)
            {
                return $"<span class='number zero'>{value}</span>";
            }
            else if (value >= 1 && value <= 3)
            {
                return $"<span class='number low'>{value}</span>";
            }
            else if (value >= 4 && value <= 7)
            {
                return $"<span class='number medium'>{value}</span>";
            }
            else
            {
                return $"<span class='number high'>{value}</span>";
            }
        }




        // تقوم هذه الدالة بتصدير بيانات GridView إلى ملف Excel عند الضغط على الزر.
        // 1. تقوم بإعداد استجابة HTTP جديدة باستخدام `Response` لإرسال الملف إلى المستخدم.
        // 2. يتم إعداد ترويسة الاستجابة (Header) لتحديد نوع الملف كملف Excel (application/vnd.ms-excel).
        // 3. يتم تعطيل تقسيم الصفحات في GridView لضمان تصدير جميع الصفوف.
        // 4. يتم إعادة تحميل البيانات في GridView باستخدام `BindGridView` بدون تقسيم الصفحات.
        // 5. يتم تصدير محتويات GridView إلى ملف Excel باستخدام `RenderControl`.
        // 6. في النهاية، يتم إرسال الملف إلى المستخدم وإنهاء الاستجابة باستخدام `Response.End()`.

        protected void btnExportToExcel_Click(object sender, EventArgs e)
        {
            // التأكد من أن GridView يحتوي على بيانات
            if (GridView1.Rows.Count > 0)
            {
                using (ExcelPackage excel = new ExcelPackage())
                {
                    // إنشاء ورقة عمل جديدة
                    var worksheet = excel.Workbook.Worksheets.Add("Orders");

                    // إضافة رؤوس الأعمدة من GridView
                    for (int i = 0; i < GridView1.HeaderRow.Cells.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = GridView1.HeaderRow.Cells[i].Text;
                        worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[1, i + 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    }

                    // إضافة بيانات الصفوف من GridView
                    for (int i = 0; i < GridView1.Rows.Count; i++)
                    {
                        for (int j = 0; j < GridView1.Rows[i].Cells.Count; j++)
                        {
                            worksheet.Cells[i + 2, j + 1].Value = GridView1.Rows[i].Cells[j].Text;
                        }
                    }

                    // إعدادات الاستجابة لتصدير الملف
                    Response.Clear();
                    Response.Buffer = true;
                    Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    Response.AddHeader("content-disposition", "attachment; filename=OrdersExport.xlsx");

                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        excel.SaveAs(memoryStream);
                        memoryStream.WriteTo(Response.OutputStream);
                        Response.Flush();
                        Response.End();
                    }
                }
            }
            else
            {
                // إذا لم تكن هناك بيانات
                Response.Write("<script>alert('لا توجد بيانات للتصدير');</script>");
            }
        }

        protected void ddlDeletePeriod_SelectedIndexChanged(object sender, EventArgs e)
        {
            pnlDateRange.Visible = (ddlDeletePeriod.SelectedValue == "-1");
        }

        protected void btnDeleteOldOrders_Click(object sender, EventArgs e)
        {
            try
            {
                if (ddlDeletePeriod.SelectedValue == "-1" &&
                    (string.IsNullOrEmpty(txtStartDate.Text) || string.IsNullOrEmpty(txtEndDate.Text)))
                {
                    ShowMessage("الرجاء تحديد نطاق التاريخ بالكامل");
                    return;
                }

                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                string whereClause;
                List<SqlParameter> parameters = new List<SqlParameter>();

                // إعداد شرط التاريخ حسب الاختيار
                if (ddlDeletePeriod.SelectedValue == "-1")
                {
                    whereClause = "[تاريخ الطلب] BETWEEN @StartDate AND @EndDate";
                    parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime)
                    {
                        Value = DateTime.Parse(txtStartDate.Text)
                    });
                    parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime)
                    {
                        Value = DateTime.Parse(txtEndDate.Text).AddDays(1).AddSeconds(-1) // نهاية اليوم
                    });
                }
                else
                {
                    int months = Convert.ToInt32(ddlDeletePeriod.SelectedValue);
                    whereClause = "DATEDIFF(MONTH, [تاريخ الطلب], GETDATE()) >= @MonthsOld";
                    parameters.Add(new SqlParameter("@MonthsOld", SqlDbType.Int)
                    {
                        Value = months
                    });
                }

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            int batchSize = 1000;
                            int totalDeleted = 0;
                            bool hasMoreRecords = true;

                            while (hasMoreRecords)
                            {
                                using (SqlCommand cmd = new SqlCommand())
                                {
                                    cmd.Connection = conn;
                                    cmd.Transaction = transaction;
                                    cmd.CommandText = $@"
                                DELETE TOP (@BatchSize)
                                FROM [ordersTable]
                                WHERE {whereClause}
                                AND [حالة الطلب] IN ('مكتمل', 'ملغي')";

                                    cmd.Parameters.AddWithValue("@BatchSize", batchSize);
                                    cmd.Parameters.AddRange(parameters.ToArray());

                                    int affected = cmd.ExecuteNonQuery();
                                    totalDeleted += affected;

                                    if (affected < batchSize)
                                    {
                                        hasMoreRecords = false;
                                    }
                                    else
                                    {
                                        System.Threading.Thread.Sleep(100); // تجنب الضغط على قاعدة البيانات
                                    }
                                }
                            }

                            transaction.Commit();
                            ShowMessage($"تم حذف {totalDeleted} طلب بنجاح");
                            BindGridView();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception("فشل في تنفيذ عملية الحذف: " + ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage("حدث خطأ أثناء حذف الطلبات: " + ex.Message);
            }
        }
        private void LoadOrderNumbers()
        {
            try
            {
                string query = @"
            SELECT TOP 500 
                ISNULL([رقم الطلب], '') as OrderNumber,
                ISNULL([اسم الموظف], '') as EmployeeName
            FROM [aboZyad].[dbo].[ordersTable] WITH (NOLOCK)
            WHERE [رقم الطلب] IS NOT NULL
            ORDER BY [رقم الطلب] DESC";

                using (SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        // تفريغ القائمة وإضافة العنصر الافتراضي
                        ddlAllOrderNumbers.Items.Clear();
                        ddlAllOrderNumbers.Items.Add(new ListItem("-- اختر رقم الطلب --", ""));

                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            int count = 0;
                            while (reader.Read())
                            {
                                string orderNumber = reader["OrderNumber"].ToString().Trim();
                                string employeeName = reader["EmployeeName"].ToString().Trim();

                                if (!string.IsNullOrEmpty(orderNumber))
                                {
                                    string displayText = !string.IsNullOrEmpty(employeeName)
                                        ? $"{orderNumber} | {employeeName}"
                                        : orderNumber;

                                    ddlAllOrderNumbers.Items.Add(new ListItem(displayText, orderNumber));
                                    count++;
                                }
                            }

                            // عرض رسالة النجاح
                            if (count > 0)
                            {
                                LabelMessage.Text = $"تم تحميل {count} طلب";
                                LabelMessage.Visible = true;
                                LabelError.Visible = false;
                            }
                            else
                            {
                                LabelMessage.Text = "لا توجد طلبات للعرض";
                                LabelMessage.Visible = true;
                                LabelError.Visible = false;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ للتصحيح
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadOrderNumbers: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                // عرض رسالة خطأ للمستخدم
                LabelError.Text = "حدث خطأ في تحميل الطلبات. الرجاء المحاولة مرة أخرى.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
            }
        }
        protected void btnDeleteOrder_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(ddlAllOrderNumbers.SelectedValue))
            {
                LabelError.Text = "الرجاء اختيار رقم الطلب";
                LabelError.Visible = true;
                return;
            }

            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = "DELETE FROM ordersTable WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    try
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", ddlAllOrderNumbers.SelectedValue);
                        con.Open();
                        int result = cmd.ExecuteNonQuery();

                        if (result > 0)
                        {
                            LabelMessage.Text = "تم حذف الطلب بنجاح";
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;
                            LoadOrderNumbers(); // إعادة تحميل القائمة
                        }
                        else
                        {
                            LabelError.Text = "لم يتم العثور على الطلب";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "حدث خطأ أثناء حذف الطلب: " + ex.Message;
                        LabelError.Visible = true;
                        LabelMessage.Visible = false;
                    }
                }
            }
        }
        private void ShowMessage(string message)
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "alert",
                $"alert('{message}');", true);
        }



        // هذه الدالة تُستخدم لتجنب حدوث خطأ في وقت التشغيل عند تصدير محتويات GridView.
        // 1. عند محاولة تصدير GridView إلى Excel أو تنفيذه خارج سياق الصفحة، قد يظهر الخطأ: 
        //    "Control 'GridView1' must be placed inside a form tag with runat=server."
        // 2. هذه الدالة تتجاوز هذا التحقق الافتراضي الخاص بـ ASP.NET الذي يتطلب أن يتم تضمين جميع العناصر 
        //    (مثل GridView) داخل علامة <form> مع خاصية runat="server".
        // 3. الدالة فارغة لأنها تُستخدم فقط لتجاوز التحقق بدون إجراء أي عملية إضافية.
        public override void VerifyRenderingInServerForm(Control control)
        {
            // Required to avoid the runtime error "Control 'GridView1' must be placed inside a form tag with runat=server."
        }



        // تقوم هذه الدالة بتعبئة القائمة المنسدلة الخاصة بتصفية حالة الطلبات (ddlStatusFilter).
        // 1. يتم الاتصال بقاعدة البيانات واستدعاء استعلام SQL لجلب جميع الحالات الفريدة من جدول الطلبات (ordersTable).
        // 2. يتم إضافة كل حالة طلب فريدة إلى القائمة المنسدلة (ddlStatusFilter) لتمكين المستخدم من تصفية الطلبات حسب حالتها.
        private void PopulateStatusFilter()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string query = "SELECT DISTINCT [حالة الطلب] FROM ordersTable";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ddlStatusFilter.Items.Add(new ListItem(reader["حالة الطلب"].ToString()));
                        }
                    }
                }
            }
        }

        // تقوم هذه الدالة بتعبئة القائمة المنسدلة الخاصة بتصفية الأقسام (ddlDepartmentFilter).
        // 1. يتم الاتصال بقاعدة البيانات واستدعاء استعلام SQL لجلب جميع الأقسام الفريدة من جدول الأقسام (Departments).
        // 2. يتم إضافة كل قسم فريد إلى القائمة المنسدلة (ddlDepartmentFilter) لتمكين المستخدم من تصفية الطلبات حسب القسم.
        private void PopulateDepartmentFilter()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string query = "SELECT DISTINCT [الأقسام] FROM Departments";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ddlDepartmentFilter.Items.Add(new ListItem(reader["الأقسام"].ToString()));
                        }
                    }
                }
            }
        }

        // تقوم هذه الدالة بمعالجة تغيير اختيار المستخدم في القائمة المنسدلة الخاصة بحالة الطلبات (ddlStatusFilter).
        // 1. عندما يختار المستخدم حالة جديدة من القائمة المنسدلة، يتم استدعاء هذه الدالة.
        // 2. يتم استدعاء الدالة BindGridView لتحديث عرض البيانات في GridView بناءً على حالة الطلب المحددة في الفلتر.

        protected void ddlStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            BindGridView();
        }

        // تقوم هذه الدالة بمعالجة تغيير اختيار المستخدم في القائمة المنسدلة الخاصة بالأقسام (ddlDepartmentFilter).
        // 1. عندما يختار المستخدم قسمًا جديدًا من القائمة المنسدلة، يتم استدعاء هذه الدالة.
        // 2. يتم استدعاء الدالة BindGridView لتحديث عرض البيانات في GridView بناءً على القسم المحدد في الفلتر.
        protected void ddlDepartmentFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            BindGridView();
        }

        // تقوم هذه الدالة بتحضير استعلام SQL ديناميكي بناءً على الفلاتر التي يحددها المستخدم.
        // 1. يتم إنشاء استعلام SQL أساسي لجلب جميع السجلات من جدول الطلبات (ordersTable).
        // 2. إذا تم تحديد فلتر معين لحالة الطلب، يتم إضافة شرط لتصفية السجلات بناءً على حالة الطلب المختارة.
        // 3. إذا تم تحديد فلتر معين للقسم، يتم إضافة شرط لتصفية السجلات بناءً على القسم المختار.
        // 4. إذا تم تحديد نطاق تواريخ (من وإلى)، يتم إضافة شرط لتصفية السجلات حسب نطاق التواريخ.
        // 5. في النهاية، يتم إرجاع استعلام SQL النهائي.
        private string PrepareQuery()
        {
            string query = "SELECT * FROM ordersTable WHERE 1=1";

            if (ddlStatusFilter.SelectedValue != "All")
            {
                query += " AND [حالة الطلب] = @RequestStatus";
            }

            if (ddlDepartmentFilter.SelectedValue != "All")
            {
                query += " AND [القسم] = @Department";
            }

            if (!string.IsNullOrEmpty(txtFromDate.Text) && !string.IsNullOrEmpty(txtToDate.Text))
            {
                query += " AND [تاريخ الطلب] BETWEEN @FromDate AND @ToDate";
            }

            if (!string.IsNullOrEmpty(txtCivilRecord.Text))
            {
                query += " AND [السجل المدني] = @CivilRecord";
            }

            return query;
        }


        // تقوم هذه الدالة بربط البيانات مع GridView بناءً على الفلاتر التي يحددها المستخدم.
        // 1. الاتصال بقاعدة البيانات باستخدام SqlConnection.
        // 2. استرجاع أسماء الأعمدة من جدول "ordersTable" في قاعدة البيانات باستخدام GetSchema، ثم تخزينها في قائمة.
        // 3. ترتيب وإضافة الأعمدة المطلوبة إلى GridView بناءً على قائمة الأعمدة المطلوبة (orderedColumns).
        // 4. إنشاء استعلام SQL ديناميكي باستخدام PrepareQuery استنادًا إلى الفلاتر المختارة من قبل المستخدم (حالة الطلب، القسم، ونطاق التواريخ).
        // 5. تنفيذ الاستعلام وجلب النتائج باستخدام SqlDataAdapter وتعبئة DataTable.
        // 6. إذا تم العثور على بيانات، يتم ربطها بــ GridView، وإذا لم يتم العثور على بيانات، يتم عرض رسالة للمستخدم.

        private void BindGridView()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                GridView1.Columns.Clear();
                // استرجاع أسماء الأعمدة من قاعدة البيانات
                DataTable schemaTable = con.GetSchema("Columns", new[] { null, null, "ordersTable", null });
                List<string> columnNames = new List<string>();
                foreach (DataRow row in schemaTable.Rows)
                {
                    columnNames.Add(row["COLUMN_NAME"].ToString());
                }
                // ترتيب الأعمدة حسب المطلوب
                List<string> orderedColumns = new List<string>
        {
    "رقم الطلب",
    "تاريخ الطلب",
    "نوع الطلب",
    "اسم الموظف",
    "الوظيفة",
    "رقم الموظف",
    "السجل المدني",
    "الجنسية",
    "رقم الجوال",
    "القسم",
    "تفاصيل مقدم الطلب",
    "حالة الطلب",
    "تم التأكيد/الإلغاء من مدير القسم",  
    "تم التأكيد/الإلغاء من قبل المشرف",
    "تم التحويل/الإلغاء من قبل المنسق",
    "سبب الإلغاء/الإعادة",
    "تفاصيل المنسق",
    "مشرف خدمات الموظفين",
    "مشرف إدارة تخطيط الموارد البشرية",
    "مشرف إدارة تقنية المعلومات",
    "مشرف مراقبة الدوام",
    "مشرف السجلات الطبية",
    "مشرف إدارة الرواتب والاستحقاقات",
    "مشرف إدارة القانونية والالتزام",
    "مشرف خدمات الموارد البشرية",
    "مشرف إدارة الإسكان",
    "مشرف قسم الملفات",
    "مشرف العيادات الخارجية",
    "مشرف التأمينات الاجتماعية",
    "مشرف وحدة مراقبة المخزون",
    "مشرف إدارة تنمية الإيرادات",
    "مشرف إدارة الأمن و السلامة",
    "مشرف الطب الاتصالي",
    "مدير الموارد البشرية",
    "نوع التحويل"
};

                // إضافة الأعمدة إلى الـ GridView
                foreach (var columnName in orderedColumns)
                {
                    if (columnNames.Contains(columnName))
                    {
                        BoundField boundField = new BoundField
                        {
                            DataField = columnName,
                            HeaderText = columnName
                        };
                        if (columnName == "تاريخ الطلب")
                        {
                            boundField.DataFormatString = "{0:yyyy-MM-dd}";
                        }
                        GridView1.Columns.Add(boundField);
                    }
                }

                // إضافة أعمدة الملفات في النهاية
                for (int i = 1; i <= 4; i++)
                {
                    if (columnNames.Contains($"ملف{i}"))
                    {
                        TemplateField attachmentField = new TemplateField();
                        attachmentField.HeaderText = $"ملف {i}";
                        attachmentField.ItemTemplate = new AttachmentTemplate(i);
                        GridView1.Columns.Add(attachmentField);
                    }
                }

                string query = PrepareQuery();
                using (SqlCommand command = new SqlCommand(query, con))
                {
                    command.Parameters.Add("@RequestStatus", SqlDbType.NVarChar).Value = ddlStatusFilter.SelectedValue;

                    if (ddlDepartmentFilter.SelectedValue != "All")
                    {
                        command.Parameters.Add("@Department", SqlDbType.NVarChar).Value = ddlDepartmentFilter.SelectedValue;
                    }

                    if (!string.IsNullOrEmpty(txtFromDate.Text) && !string.IsNullOrEmpty(txtToDate.Text))
                    {
                        command.Parameters.AddWithValue("@FromDate", DateTime.Parse(txtFromDate.Text));
                        command.Parameters.AddWithValue("@ToDate", DateTime.Parse(txtToDate.Text));
                    }

                    if (!string.IsNullOrEmpty(txtCivilRecord.Text))
                    {
                        command.Parameters.Add("@CivilRecord", SqlDbType.NVarChar).Value = txtCivilRecord.Text;
                    }

                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        if (dataTable.Rows.Count > 0)
                        {
                            GridView1.DataSource = dataTable;
                            GridView1.DataBind();
                        }
                        else
                        {
                            lblMessage.Text = "لا توجد طلبات مرتبطة بهذا السجل المدني.";
                        }
                    }
                }

            }
        }
        protected void btnFilterByCivilRecord_Click(object sender, EventArgs e)
        {
            BindGridView();
        }



        // تقوم هذه الدالة بمعالجة تغيير صفحة GridView عند التنقل بين الصفحات.
        // 1. يتم تعيين PageIndex الخاص بـ GridView إلى الصفحة الجديدة التي اختارها المستخدم.
        // 2. يتم استدعاء BindGridView لإعادة تحميل البيانات مع الصفحة الجديدة.
        // 3. تضمن هذه الدالة أن GridView يعرض البيانات بشكل صحيح عند الانتقال إلى صفحة جديدة.
        protected void GridView1_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            GridView1.PageIndex = e.NewPageIndex;
            BindGridView();
        }

        // تقوم هذه الدالة بتصفية البيانات في GridView بناءً على نطاق التواريخ الذي أدخله المستخدم.
        // 1. تتحقق أولاً من أن التواريخ المدخلة في حقلي txtFromDate و txtToDate صالحة باستخدام DateTime.TryParse.
        // 2. إذا كانت التواريخ صحيحة، يتم استدعاء BindGridView لإعادة تحميل البيانات المصفاة بناءً على نطاق التواريخ.
        // 3. إذا كانت التواريخ غير صالحة، يتم عرض رسالة خطأ للمستخدم في lblMessage.
        protected void btnFilterByDate_Click(object sender, EventArgs e)
        {
            DateTime fromDate, toDate;
            if (DateTime.TryParse(txtFromDate.Text, out fromDate) && DateTime.TryParse(txtToDate.Text, out toDate))
            {
                BindGridView();
            }
            else
            {
                lblMessage.Text = "يرجى إدخال تواريخ صحيحة.";
            }
        }

        // تقوم هذه الدالة بإزالة جميع فلاتر التواريخ المطبقة على البيانات.
        // 1. تقوم بتفريغ حقول التواريخ (txtFromDate و txtToDate) لجعلها فارغة.
        // 2. بعد إزالة الفلاتر، يتم استدعاء BindGridView لإعادة تحميل البيانات بدون تصفية حسب نطاق التواريخ.
        protected void btnClearFilters_Click(object sender, EventArgs e)
        {
            txtFromDate.Text = "";
            txtToDate.Text = "";
            txtCivilRecord.Text = "";
            BindGridView();
        }

        // تقوم هذه الدالة بمعالجة حدث ترتيب الأعمدة في GridView.
        // 1. يتم استدعاء BindGridView لإعادة تحميل البيانات في GridView.
        // 2. يتم تحويل البيانات المعروضة في GridView إلى DataTable.
        // 3. إذا كانت البيانات صالحة (غير فارغة)، يتم تحديد عمود الترتيب واتجاه الترتيب (تصاعدي أو تنازلي) بناءً على تعبير الترتيب (SortExpression).
        // 4. يتم تحديث مصدر بيانات GridView بناءً على العمود واتجاه الترتيب المختار، ثم يتم إعادة عرض البيانات باستخدام DataBind.
        protected void GridView1_Sorting(object sender, GridViewSortEventArgs e)
        {
            BindGridView();
            DataTable dataTable = GridView1.DataSource as DataTable;
            if (dataTable != null)
            {
                dataTable.DefaultView.Sort = e.SortExpression + " " + GetSortDirection(e.SortExpression);
                GridView1.DataSource = dataTable;
                GridView1.DataBind();
            }
        }

        protected void ExportFilteredResultsToExcel(object sender, EventArgs e)
        {
            try
            {
                // تحقق من وجود سجل مدني مدخل
                if (string.IsNullOrEmpty(txtCivilRecord.Text))
                {
                    lblMessage.Text = "الرجاء إدخال رقم السجل المدني أولاً";
                    return;
                }

                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();

                    // استعلام SQL لجلب البيانات باستثناء اسم الموظف ورقم الموظف
                    string query = @"
                SELECT 
                    [رقم الطلب],
                    [تاريخ الطلب],
                    [نوع الطلب],
                    [الوظيفة],
                    [القسم],
                    [تفاصيل مقدم الطلب],
                    [حالة الطلب]
                FROM [aboZyad].[dbo].[ordersTable]
                WHERE [السجل المدني] = @CivilRecord";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        // إضافة المعاملات للاستعلام
                        cmd.Parameters.Add("@CivilRecord", SqlDbType.NVarChar).Value = txtCivilRecord.Text;

                        // استعلام منفصل لجلب اسم الموظف ورقم الموظف فقط للعنوان
                        string employeeInfoQuery = @"
                    SELECT TOP 1 
                        [اسم الموظف],
                        [رقم الموظف]
                    FROM [aboZyad].[dbo].[ordersTable]
                    WHERE [السجل المدني] = @CivilRecord";

                        using (SqlCommand employeeInfoCmd = new SqlCommand(employeeInfoQuery, con))
                        {
                            employeeInfoCmd.Parameters.Add("@CivilRecord", SqlDbType.NVarChar).Value = txtCivilRecord.Text;
                            SqlDataReader reader = employeeInfoCmd.ExecuteReader();
                            string employeeName = "غير متوفر";
                            string employeeNumber = "غير متوفر";

                            if (reader.Read())
                            {
                                employeeName = reader["اسم الموظف"].ToString();
                                employeeNumber = reader["رقم الموظف"].ToString();
                            }
                            reader.Close();

                            using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                            {
                                DataTable dt = new DataTable();
                                adapter.Fill(dt);

                                if (dt.Rows.Count > 0)
                                {
                                    using (ExcelPackage package = new ExcelPackage())
                                    {
                                        // إنشاء ورقة عمل جديدة
                                        ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("نتائج البحث");

                                        // إضافة العنوان
                                        worksheet.Cells[1, 1].Value = $"نتائج البحث للسجل المدني: {txtCivilRecord.Text} - اسم الموظف: {employeeName} - رقم الموظف: {employeeNumber}";
                                        worksheet.Cells[1, 1, 1, dt.Columns.Count].Merge = true; // دمج الخلايا
                                        worksheet.Cells[1, 1].Style.Font.Bold = true;
                                        worksheet.Cells[1, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;

                                        // إضافة رؤوس الأعمدة
                                        for (int i = 0; i < dt.Columns.Count; i++)
                                        {
                                            worksheet.Cells[2, i + 1].Value = dt.Columns[i].ColumnName;
                                            worksheet.Cells[2, i + 1].Style.Font.Bold = true;
                                            worksheet.Cells[2, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                                            worksheet.Cells[2, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                                        }

                                        // إضافة البيانات
                                        worksheet.Cells[3, 1].LoadFromDataTable(dt, false);
                                        // تنسيق عمود التواريخ
                                        worksheet.Column(2).Style.Numberformat.Format = "dd/MM/yyyy";
                                        // ضبط عرض الأعمدة تلقائيًا
                                        worksheet.Cells.AutoFitColumns();

                                        // تحضير الملف للتحميل
                                        Response.Clear();
                                        Response.Buffer = true;
                                        Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                                        Response.AddHeader("content-disposition", $"attachment; filename=نتائج_البحث_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

                                        using (MemoryStream memoryStream = new MemoryStream())
                                        {
                                            package.SaveAs(memoryStream);
                                            memoryStream.WriteTo(Response.OutputStream);
                                            Response.Flush();
                                            Response.End();
                                        }
                                    }
                                }
                                else
                                {
                                    lblMessage.Text = "لا توجد بيانات للتصدير.";
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lblMessage.Text = "حدث خطأ أثناء تصدير البيانات.";
                LogError(ex);
            }
        }












        // تقوم هذه الدالة بتحديد اتجاه الفرز (تصاعدي أو تنازلي) بناءً على العمود الذي يختاره المستخدم.
        // 1. تبدأ بتحديد الاتجاه الافتراضي للفرز وهو تصاعدي ("ASC").
        // 2. تتحقق مما إذا كان العمود الحالي هو نفس العمود الذي تم فرزه في السابق (باستخدام ViewState).
        // 3. إذا كان العمود هو نفس العمود الذي تم فرزه في السابق، تتحقق مما إذا كان اتجاه الفرز السابق هو تصاعدي ("ASC").
        // 4. إذا كان الفرز السابق تصاعديًا، يتم تغيير اتجاه الفرز إلى تنازلي ("DESC").
        // 5. يتم تخزين العمود الحالي واتجاه الفرز في ViewState للحفاظ على حالة الفرز.
        // 6. تُعيد الدالة اتجاه الفرز الحالي ("ASC" أو "DESC").
        private string GetSortDirection(string column)
        {
            // إدارة حالة الفرز (تصاعدي أو تنازلي)
            string sortDirection = "ASC";
            string previousSort = ViewState["SortExpression"] as string;
            if (previousSort != null && previousSort == column)
            {
                string previousDirection = ViewState["SortDirection"] as string;
                if (previousDirection != null && previousDirection == "ASC")
                {
                    sortDirection = "DESC";
                }
            }
            ViewState["SortDirection"] = sortDirection;
            ViewState["SortExpression"] = column;
            return sortDirection;
        }
        // تقوم هذه الدالة بتعيين العنصر المحدد في القائمة المنسدلة (ddlDepartmentFilter) بناءً على صلاحيات المستخدم المخزنة في الجلسة.
        // 1. يتم استرجاع قيمة صلاحيات المستخدم المخزنة في الجلسة (Session["UserPermission"]).
        // 2. إزالة التحديد الحالي من جميع العناصر في القائمة المنسدلة لضمان عدم وجود عنصر محدد مسبقاً.
        // 3. إذا كانت الصلاحية المخزنة في الجلسة غير فارغة، يتم التكرار على عناصر القائمة.
        // 4. عند العثور على عنصر يطابق الصلاحية المخزنة في الجلسة، يتم تعيينه كعنصر محدد في القائمة المنسدلة.
        private void SetDropDownListSelectedItem()
        {
            // الحصول على القيمة المخزنة في الجلسة
            string textToFind = Session["UserPermission"]?.ToString();

            // إزالة التحديد الحالي من جميع العناصر في القائمة
            foreach (ListItem item in ddlDepartmentFilter.Items)
            {
                item.Selected = false;
            }

            // التحقق من أن القيمة المخزنة في الجلسة ليست فارغة
            if (!string.IsNullOrEmpty(textToFind))
            {
                // تحديد العنصر المناسب في القائمة
                foreach (ListItem item in ddlDepartmentFilter.Items)
                {
                    if (item.Text.Trim() == textToFind.Trim())
                    {
                        item.Selected = true;
                        break;
                    }
                }
            }
        }

        // تقوم هذه الدالة بتلوين الصفوف في GridView بناءً على عمر الطلب (عدد الأيام منذ تقديم الطلب).
        // 1. يتم التحقق من أن الصف هو من نوع DataRow (صف بيانات).
        // 2. يتم محاولة تحويل النص الموجود في الخلية الثانية (التي تحتوي على تاريخ الطلب) إلى نوع DateTime.
        // 3. إذا كان التاريخ صالحًا، يتم حساب عدد الأيام منذ تاريخ الطلب وحتى اليوم الحالي.
        // 4. بناءً على عدد الأيام، يتم تطبيق لون خلفية مختلف للصف:
        //    - أخضر فاتح جدًا: للطلبات التي مضى عليها 7 أيام أو أقل.
        //    - أزرق فاتح: للطلبات التي مضى عليها بين 7 و14 يومًا.
        //    - أصفر فاتح: للطلبات التي مضى عليها بين 15 و21 يومًا.
        //    - برتقالي فاتح: للطلبات التي مضى عليها بين 22 و30 يومًا.
        //    - أحمر غامق: للطلبات التي مضى عليها أكثر من 30 يومًا.

        protected void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                DateTime orderDate;
                if (DateTime.TryParse(e.Row.Cells[1].Text, out orderDate)) // التأكد من صحة الخلية للعمود
                {
                    // حساب عمر الطلب
                    double totalDays = (DateTime.Now - orderDate).TotalDays;

                    if (totalDays <= 7)
                    {
                        e.Row.BackColor = System.Drawing.Color.FromArgb(198, 239, 206); // أخضر فاتح جدًا للطلبات الحديثة
                    }
                    else if (totalDays <= 14)
                    {
                        e.Row.BackColor = System.Drawing.Color.FromArgb(204, 229, 255); // أزرق فاتح للطلبات بين 7 و14 يومًا
                    }
                    else if (totalDays <= 21)
                    {
                        e.Row.BackColor = System.Drawing.Color.FromArgb(255, 255, 204); // أصفر فاتح للطلبات بين 15 و21 يومًا
                    }
                    else if (totalDays <= 30)
                    {
                        e.Row.BackColor = System.Drawing.Color.FromArgb(255, 224, 204); // برتقالي فاتح للطلبات بين 22 و30 يومًا
                    }
                    else
                    {
                        e.Row.BackColor = System.Drawing.Color.FromArgb(255, 100, 100); // أحمر غامق للطلبات التي مضى عليها أكثر من شهر
                    }
                }
            }
        }






        // تقوم هذه الدالة بتصدير تقارير إحصائية شاملة إلى ملف Excel عند نقر المستخدم على الزر.
        // 1. يتم إعداد الاستجابة لتصدير ملف Excel باستخدام الترخيص غير التجاري لـ EPPlus.
        // 2. يتم إنشاء ورقة عمل جديدة في ملف Excel لكل تقرير من التقارير التالية:
        //    - التقرير الأول: إجمالي الطلبات حسب حالة الطلب (مثل المكتملة والمرفوضة).
        //    - التقرير الثاني: التقارير الزمنية حسب الأقسام (مثل الطلبات المتأخرة وأطول وأقصر وقت لإنجاز الطلب).
        //    - التقرير الثالث: تحليل الأداء حسب المشرفين (عدد الطلبات المكتملة والمرفوضة لكل مشرف).
        //    - التقرير الرابع: توزيع الطلبات حسب نوع الطلب (عدد الطلبات حسب كل نوع).
        // 3. يتم جلب البيانات من قاعدة البيانات لكل تقرير باستخدام استعلامات SQL مخصصة.
        // 4. يتم تنسيق الأعمدة تلقائيًا لملاءمة البيانات.
        // 5. في النهاية، يتم إرسال ملف Excel إلى المستخدم كملف مرفق يمكن تحميله.

        protected void btnExportDetailedStatisticsToExcel_Click(object sender, EventArgs e)
        {
            // إعداد الترخيص للاستخدام غير التجاري
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            // إعداد الاستجابة لتصدير ملف Excel
            HttpContext.Current.Response.Clear();
            HttpContext.Current.Response.Buffer = true;
            HttpContext.Current.Response.AddHeader("content-disposition", "attachment;filename=DetailedStatisticsExport.xlsx");
            HttpContext.Current.Response.Charset = "";
            HttpContext.Current.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            using (ExcelPackage package = new ExcelPackage())
            {
                // الاتصال بقاعدة البيانات
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    // إنشاء ورقة عمل جديدة
                    ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("التقارير الشاملة");

                    // التقرير الأول: إجمالي الطلبات حسب حالة الطلب
                    worksheet.Cells[1, 1].Value = "إجمالي الطلبات حسب حالة الطلب:";
                    worksheet.Cells[1, 1, 1, 5].Merge = true;
                    worksheet.Cells[1, 1].Style.Font.Bold = true;
                    worksheet.Cells[1, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 1].Value = "القسم";
                    worksheet.Cells[2, 2].Value = "عدد الطلبات المقدمة";
                    worksheet.Cells[2, 3].Value = "مكتملة";
                    worksheet.Cells[2, 4].Value = "مرفوضة";
                    worksheet.Cells[2, 5].Value = "نسبة الإنجاز";

                    int row = 3;  // البدء من الصف الثالث بعد العناوين

                    // استعلام الأقسام
                    string departmentQuery = "SELECT [القسم], COUNT(*) AS [عدد الطلبات المقدمة], " +
                                             "SUM(CASE WHEN [حالة الطلب] = 'مقبول' THEN 1 ELSE 0 END) AS [مكتملة], " +
                                             "SUM(CASE WHEN [حالة الطلب] = 'مرفوض' THEN 1 ELSE 0 END) AS [مرفوضة], " +
                                             "CAST(SUM(CASE WHEN [حالة الطلب] = 'مقبول' THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*) * 100 AS [نسبة الإنجاز] " +
                                             "FROM ordersTable GROUP BY [القسم]";
                    SqlCommand deptCmd = new SqlCommand(departmentQuery, connection);
                    SqlDataReader deptReader = deptCmd.ExecuteReader();
                    while (deptReader.Read())
                    {
                        worksheet.Cells[row, 1].Value = deptReader["القسم"];
                        worksheet.Cells[row, 2].Value = deptReader["عدد الطلبات المقدمة"];
                        worksheet.Cells[row, 3].Value = deptReader["مكتملة"];
                        worksheet.Cells[row, 4].Value = deptReader["مرفوضة"];
                        worksheet.Cells[row, 5].Value = deptReader["نسبة الإنجاز"].ToString() + "%";
                        row++;
                    }
                    deptReader.Close();

                    row = 3;  // إعادة تعيين الصف

                    // التقرير الثاني: التقارير الزمنية حسب الأقسام
                    worksheet.Cells[1, 7].Value = "التقارير الزمنية حسب الأقسام:";
                    worksheet.Cells[1, 7, 1, 10].Merge = true;
                    worksheet.Cells[1, 7].Style.Font.Bold = true;
                    worksheet.Cells[1, 7].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 7].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 7].Value = "القسم";
                    worksheet.Cells[2, 8].Value = "عدد الطلبات المتأخرة";
                    worksheet.Cells[2, 9].Value = "أطول وقت لإنجاز طلب";
                    worksheet.Cells[2, 10].Value = "أقصر وقت لإنجاز طلب";

                    string timeQuery = "SELECT [القسم], COUNT(*) AS [عدد الطلبات المتأخرة], " +
                                       "MAX(DATEDIFF(day, [تاريخ الطلب], GETDATE())) AS [أطول وقت], " +
                                       "MIN(DATEDIFF(day, [تاريخ الطلب], GETDATE())) AS [أقصر وقت] " +
                                       "FROM ordersTable WHERE [حالة الطلب] = 'مقبول' GROUP BY [القسم]";
                    SqlCommand timeCmd = new SqlCommand(timeQuery, connection);
                    SqlDataReader timeReader = timeCmd.ExecuteReader();
                    while (timeReader.Read())
                    {
                        worksheet.Cells[row, 7].Value = timeReader["القسم"];
                        worksheet.Cells[row, 8].Value = timeReader["عدد الطلبات المتأخرة"];
                        worksheet.Cells[row, 9].Value = timeReader["أطول وقت"];
                        worksheet.Cells[row, 10].Value = timeReader["أقصر وقت"];
                        row++;
                    }
                    timeReader.Close();

                    // إعادة تعيين الصف
                    row = 3;

                    // التقرير الثالث: تحليل الأداء حسب المشرفين
                    worksheet.Cells[1, 13].Value = "تحليل الأداء حسب المشرفين:";
                    worksheet.Cells[1, 13, 1, 16].Merge = true;
                    worksheet.Cells[1, 13].Style.Font.Bold = true;
                    worksheet.Cells[1, 13].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 13].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 13].Value = "المشرف";
                    worksheet.Cells[2, 14].Value = "عدد الطلبات";
                    worksheet.Cells[2, 15].Value = "مكتملة";
                    worksheet.Cells[2, 16].Value = "مرفوضة";

                    string[] supervisors = {
                "مشرف خدمات الموظفين",
                "مشرف إدارة تخطيط الموارد البشرية",
                "مشرف إدارة تقنية المعلومات",
                "مشرف مراقبة الدوام",
                "مشرف السجلات الطبية",
                "مشرف إدارة الرواتب والاستحقاقات",
                "مشرف إدارة القانونية والالتزام",
                "مشرف خدمات الموارد البشرية",
                "مشرف إدارة الإسكان",
                "مشرف قسم الملفات",
                "مشرف العيادات الخارجية",
                "مشرف التأمينات الاجتماعية",
                "مشرف وحدة مراقبة المخزون",
                "مشرف إدارة تنمية الإيرادات",
                "مشرف إدارة الأمن و السلامة",
                "مشرف الطب الاتصالي"
            };

                    foreach (var supervisor in supervisors)
                    {
                        string supervisorQuery = $"SELECT '{supervisor}' AS [المشرف], COUNT(*) AS [عدد الطلبات], " +
                                                 $"SUM(CASE WHEN [حالة الطلب] = 'مقبول' THEN 1 ELSE 0 END) AS [مكتملة], " +
                                                 $"SUM(CASE WHEN [حالة الطلب] = 'مرفوض' THEN 1 ELSE 0 END) AS [مرفوضة] " +
                                                 $"FROM ordersTable WHERE [{supervisor}] IS NOT NULL";

                        SqlCommand supCmd = new SqlCommand(supervisorQuery, connection);
                        SqlDataReader supReader = supCmd.ExecuteReader();
                        while (supReader.Read())
                        {
                            worksheet.Cells[row, 13].Value = supReader["المشرف"];
                            worksheet.Cells[row, 14].Value = supReader["عدد الطلبات"];
                            worksheet.Cells[row, 15].Value = supReader["مكتملة"];
                            worksheet.Cells[row, 16].Value = supReader["مرفوضة"];
                            row++;
                        }
                        supReader.Close();
                    }

                    // التقرير الرابع: التوزيع حسب نوع الطلبات
                    worksheet.Cells[1, 19].Value = "التوزيع حسب نوع الطلبات:";
                    worksheet.Cells[1, 19, 1, 20].Merge = true;
                    worksheet.Cells[1, 19].Style.Font.Bold = true;
                    worksheet.Cells[1, 19].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 19].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 19].Value = "نوع الطلب";
                    worksheet.Cells[2, 20].Value = "إجمالي الطلبات";
                    row = 3; // بدء إدخال البيانات من الصف الثالث

                    // استعلام أنواع الطلبات
                    string requestTypeQuery = "SELECT [نوع الطلب], COUNT(*) AS [إجمالي الطلبات] " +
                                              "FROM ordersTable GROUP BY [نوع الطلب]";
                    SqlCommand reqTypeCmd = new SqlCommand(requestTypeQuery, connection);
                    SqlDataReader reqTypeReader = reqTypeCmd.ExecuteReader();
                    while (reqTypeReader.Read())
                    {
                        worksheet.Cells[row, 19].Value = reqTypeReader["نوع الطلب"];
                        worksheet.Cells[row, 20].Value = reqTypeReader["إجمالي الطلبات"];
                        row++;
                    }
                    reqTypeReader.Close();

                    // تنسيق الأعمدة
                    worksheet.Cells[1, 1, row - 1, 20].AutoFitColumns(); // ضبط حجم الأعمدة تلقائيًا

                    // إخراج الملف
                    var fileContents = package.GetAsByteArray();

                    // كتابة الملف إلى الاستجابة
                    HttpContext.Current.Response.BinaryWrite(fileContents);
                    HttpContext.Current.Response.Flush(); // اكتب أي بيانات في المخزن المؤقت

                    // إنهاء الطلب بشكل آمن
                    HttpContext.Current.ApplicationInstance.CompleteRequest();
                }
            }
        }


        // تقوم هذه الدالة بتحميل إجمالي الطلبات الشهرية إلى GridView عند استدعائها.
        // 1. الاتصال بقاعدة البيانات.
        // 2. تنفيذ استعلام SQL للحصول على إجمالي الطلبات الشهرية لكل شهر.
        // 3. تعبئة GridView بالبيانات المسترجعة من قاعدة البيانات.
        // 4. عرض النتائج الشهرية في GridView أو رسالة في حال عدم وجود بيانات.

        protected void LoadMonthlyTotalsToGrid(object sender, EventArgs e)
        {
            // الكود الخاص بتحميل الطلبات الشهرية
        }

        // استعلام الطلبات الشهرية-العمود الخامس
        // تقوم هذه الدالة بتحميل إجمالي الطلبات الشهرية للسنة الحالية وعرضها باستخدام Literal.
        // 1. يتم الاتصال بقاعدة البيانات.
        // 2. تنفيذ استعلام SQL لجلب عدد الطلبات لكل شهر في السنة الحالية.
        // 3. يتم استخدام SqlDataReader لقراءة النتائج.
        // 4. يتم بناء المحتوى باستخدام StringBuilder، حيث يتم عرض اسم الشهر وعدد الطلبات لكل شهر.
        // 5. يتم تعيين النص النهائي لـ StringBuilder في عنصر Literal لعرضه على الصفحة.

        private void BindGridWithMonthlyTotals()
        {
            StringBuilder detailsContent = new StringBuilder();
            StringBuilder summaryContent = new StringBuilder();
            int totalRequests = 0;

            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"
            SELECT 
                DATENAME(MONTH, [تاريخ الطلب]) AS [الشهر],
                COUNT(*) AS [عدد الطلبات],
                MONTH([تاريخ الطلب]) AS MonthNumber
            FROM ordersTable
            WHERE YEAR([تاريخ الطلب]) = YEAR(GETDATE())
            GROUP BY MONTH([تاريخ الطلب]), DATENAME(MONTH, [تاريخ الطلب])
            ORDER BY MonthNumber";

                try
                {
                    con.Open();
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string month = reader["الشهر"].ToString();
                            int requestCount = Convert.ToInt32(reader["عدد الطلبات"]);
                            totalRequests += requestCount;

                            detailsContent.Append($@"
                        <div class='department-row'>
                            <strong>{month}:</strong>
                            <span class='{GetValueClass(requestCount)}'>عدد الطلبات: {requestCount}</span>
                        </div>");
                        }
                    }

                    summaryContent.Append($@"
                <div class='summary-stats'>
                    <strong class='completed'>إجمالي الطلبات: {totalRequests}</strong> | 
                    <strong class='avg-completion-time'>متوسط الطلبات الشهري: {(totalRequests / 12.0):F2}</strong>
                </div>");

                    MonthlySummaryStats.Text = summaryContent.ToString();
                    MonthlyTotalsLiteral.Text = detailsContent.ToString();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات الشهرية: {ex.Message}");
                }
            }
        }






        // تعريف الكائنات كمثال
        // تمثل هذه الفئة (Department) بيانات قسم معين في نظام الطلبات.
        // تحتوي الفئة على الخصائص التالية:
        // 1. Name: اسم القسم.
        // 2. Accepted: عدد الطلبات التي تم قبولها في هذا القسم.
        // 3. Rejected: عدد الطلبات التي تم رفضها في هذا القسم.
        // 4. Pending: عدد الطلبات المعلقة في هذا القسم.
        // 5. CompletionPercentage: نسبة إكمال الطلبات في هذا القسم.
        public class Department
        {
            public string Name { get; set; }
            public int Accepted { get; set; }
            public int Rejected { get; set; }
            public int Pending { get; set; }
            public double CompletionPercentage { get; set; }
        }

        // تمثل هذه الفئة (Employee) بيانات موظف معين في نظام الطلبات.
        // تحتوي الفئة على الخصائص التالية:
        // 1. Name: اسم الموظف.
        // 2. SubmittedRequests: عدد الطلبات التي قدمها الموظف.
        // 3. CompletedRequests: عدد الطلبات التي تم إنجازها من قبل الموظف.
        // 4. RejectedRequests: عدد الطلبات التي تم رفضها من قبل الموظف.
        public class Employee
        {
            public string Name { get; set; }
            public int SubmittedRequests { get; set; }
            public int CompletedRequests { get; set; }
            public int RejectedRequests { get; set; }
        }

        // تمثل هذه الفئة (RequestType) نوع الطلبات في نظام إدارة الطلبات.
        // تحتوي الفئة على الخصائص التالية:
        // 1. TypeName: اسم نوع الطلب.
        // 2. TotalRequests: إجمالي عدد الطلبات التي تنتمي إلى هذا النوع.
        public class RequestType
        {
            public string TypeName { get; set; }
            public int TotalRequests { get; set; }
        }


        // تقوم هذه الدالة بإنشاء وتصدير تقارير مفصلة عن الطلبات إلى ملف Excel.
        // 1. يتم الاتصال بقاعدة البيانات واسترجاع بيانات الطلبات من الجداول المختلفة.
        // 2. يتم إنشاء ورقة Excel باستخدام مكتبة EPPlus، ويتم تقسيم التقارير إلى أقسام متعددة:
        //    - التقرير الأول: إجمالي الطلبات حسب حالة الطلب لكل قسم.
        //    - التقرير الثاني: التقارير الزمنية لكل قسم (أطول وأقصر وقت لإنجاز الطلبات وعدد الطلبات المتأخرة).
        //    - التقرير الثالث: تحليل الأداء حسب الموظفين (عدد الطلبات المقدمة والمكتملة والمرفوضة لكل موظف).
        //    - التقرير الرابع: تحليل الأداء حسب المشرفين (عدد الطلبات المكتملة، المرفوضة، وتحت التنفيذ لكل مشرف).
        //    - التقرير الخامس: توزيع الطلبات حسب نوع الطلب.
        //    - التقرير السادس: إجمالي الطلبات الشهرية للسنة الحالية.
        // 3. بعد جلب البيانات، يتم تنسيق التقرير وتطبيق الأنماط (مثل دمج الخلايا وتلوين العناوين).
        // 4. في النهاية، يتم تصدير التقرير إلى ملف Excel وإرساله إلى المستخدم كملف مرفق للتحميل.
        private void GenerateAllReports()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                // إنشاء ورقة Excel جديدة للتقارير المفصلة
                using (ExcelPackage package = new ExcelPackage())
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("التقارير المفصلة");

                    // التقرير الأول: إجمالي الطلبات حسب حالة الطلب
                    worksheet.Cells[1, 1].Value = "إجمالي الطلبات حسب حالة الطلب:";
                    worksheet.Cells[1, 1, 1, 6].Merge = true;
                    worksheet.Cells[1, 1].Style.Font.Bold = true;
                    worksheet.Cells[1, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 1].Value = "القسم";
                    worksheet.Cells[2, 2].Value = "عدد الطلبات المقدمة";
                    worksheet.Cells[2, 3].Value = "مكتملة";
                    worksheet.Cells[2, 4].Value = "مرفوضة";
                    worksheet.Cells[2, 5].Value = "تحت التنفيذ";
                    worksheet.Cells[2, 6].Value = "نسبة الإنجاز";

                    int row = 3;

                    // استعلام حالة الطلبات مع إضافة عدد الطلبات المقدمة
                    string statusQuery = @"
            SELECT [القسم], 
                   COUNT(*) AS [عدد الطلبات المقدمة],
                   SUM(CASE WHEN [حالة الطلب] = 'مكتملة' THEN 1 ELSE 0 END) AS [مكتملة],
                   SUM(CASE WHEN [حالة الطلب] = 'مرفوضة' THEN 1 ELSE 0 END) AS [مرفوضة],
                   SUM(CASE WHEN [حالة الطلب] IN ('(A)', '(B)', '(C)', '(D)') THEN 1 ELSE 0 END) AS [تحت التنفيذ],
                   SUM(CASE WHEN [حالة الطلب] = 'مقبول' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS [نسبة الإنجاز]
            FROM ordersTable
            GROUP BY [القسم]";

                    SqlCommand statusCmd = new SqlCommand(statusQuery, connection);
                    SqlDataReader statusReader = statusCmd.ExecuteReader();
                    while (statusReader.Read())
                    {
                        worksheet.Cells[row, 1].Value = statusReader["القسم"];
                        worksheet.Cells[row, 2].Value = statusReader["عدد الطلبات المقدمة"];
                        worksheet.Cells[row, 3].Value = statusReader["مكتملة"];
                        worksheet.Cells[row, 4].Value = statusReader["مرفوضة"];
                        worksheet.Cells[row, 5].Value = statusReader["تحت التنفيذ"];
                        worksheet.Cells[row, 6].Value = Math.Round(Convert.ToDouble(statusReader["نسبة الإنجاز"]), 2) + "%";
                        row++;
                    }
                    statusReader.Close();

                    // إضافة عمود فارغ للفصل بين الجداول
                    row = 3;
                    worksheet.Cells[1, 7].Value = "";

                    // التقرير الثاني: التقارير الزمنية حسب الأقسام
                    worksheet.Cells[1, 8].Value = "التقارير الزمنية حسب الأقسام:";
                    worksheet.Cells[1, 8, 1, 11].Merge = true;
                    worksheet.Cells[1, 8].Style.Font.Bold = true;
                    worksheet.Cells[1, 8].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 8].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 8].Value = "القسم";
                    worksheet.Cells[2, 9].Value = "أطول وقت لإنجاز طلب";
                    worksheet.Cells[2, 10].Value = "أقصر وقت لإنجاز طلب";
                    worksheet.Cells[2, 11].Value = "عدد الطلبات المتأخرة";

                    row = 3;

                    string timeQuery = @"
            SELECT [القسم], 
                   MAX(DATEDIFF(day, [تاريخ الطلب], GETDATE())) AS LongestTime,
                   MIN(DATEDIFF(day, [تاريخ الطلب], GETDATE())) AS ShortestTime,
                   SUM(CASE WHEN DATEDIFF(day, [تاريخ الطلب], GETDATE()) > 7 THEN 1 ELSE 0 END) AS LateRequests
            FROM ordersTable
            WHERE [حالة الطلب] = 'مقبول'
            GROUP BY [القسم]";

                    SqlCommand timeCmd = new SqlCommand(timeQuery, connection);
                    SqlDataReader timeReader = timeCmd.ExecuteReader();
                    while (timeReader.Read())
                    {
                        worksheet.Cells[row, 8].Value = timeReader["القسم"];
                        worksheet.Cells[row, 9].Value = timeReader["LongestTime"];
                        worksheet.Cells[row, 10].Value = timeReader["ShortestTime"];
                        worksheet.Cells[row, 11].Value = timeReader["LateRequests"];
                        row++;
                    }
                    timeReader.Close();

                    // إضافة عمود فارغ للفصل بين الجداول
                    row = 3;
                    worksheet.Cells[1, 12].Value = "";

                    // التقرير الثالث: تحليل الأداء حسب الموظفين
                    worksheet.Cells[1, 13].Value = "تحليل الأداء حسب الموظفين:";
                    worksheet.Cells[1, 13, 1, 16].Merge = true;
                    worksheet.Cells[1, 13].Style.Font.Bold = true;
                    worksheet.Cells[1, 13].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 13].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 13].Value = "الموظف";
                    worksheet.Cells[2, 14].Value = "مكتملة";
                    worksheet.Cells[2, 15].Value = "مرفوضة";
                    worksheet.Cells[2, 16].Value = "الطلبات المقدمة";

                    row = 3;

                    string employeeQuery = @"
            SELECT [اسم الموظف], 
                   COUNT(*) AS SubmittedRequests,
                   SUM(CASE WHEN [حالة الطلب] = 'مقبول' THEN 1 ELSE 0 END) AS CompletedRequests,
                   SUM(CASE WHEN [حالة الطلب] = 'مرفوض' THEN 1 ELSE 0 END) AS RejectedRequests
            FROM ordersTable
            GROUP BY [اسم الموظف]";

                    SqlCommand employeeCmd = new SqlCommand(employeeQuery, connection);
                    SqlDataReader employeeReader = employeeCmd.ExecuteReader();
                    while (employeeReader.Read())
                    {
                        worksheet.Cells[row, 13].Value = employeeReader["اسم الموظف"];
                        worksheet.Cells[row, 14].Value = employeeReader["CompletedRequests"];
                        worksheet.Cells[row, 15].Value = employeeReader["RejectedRequests"];
                        worksheet.Cells[row, 16].Value = employeeReader["SubmittedRequests"];
                        row++;
                    }
                    employeeReader.Close();

                    // التكرار على الأعمدة من 16 إلى 32 (كل مشرف) للحصول على الطلبات "تحت التنفيذ"
                    foreach (GridViewRow gridViewRow in GridView1.Rows)
                    {
                        int countUnderExecution = 0;

                        // تأكد من أن الصف يحتوي على عدد كافٍ من الأعمدة
                        for (int i = 16; i <= 32 && i < gridViewRow.Cells.Count; i++) // تأكد من النطاق
                        {
                            string cellValue = gridViewRow.Cells[i].Text?.Trim() ?? string.Empty;

                            if (cellValue == "الطلب تحت التنفيذ")
                            {
                                countUnderExecution++;
                            }
                        }

                        // قم بإضافة عدد الطلبات "تحت التنفيذ" في العمود 5
                        worksheet.Cells[row, 5].Value = countUnderExecution;
                    }




                    // التقرير الرابع: تحليل الأداء حسب المشرفين
                    row = 1;
                    worksheet.Cells[1, 19].Value = "تحليل الأداء حسب المشرفين:";
                    worksheet.Cells[1, 19, 1, 23].Merge = true;
                    worksheet.Cells[1, 19].Style.Font.Bold = true;
                    worksheet.Cells[1, 19].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 19].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 19].Value = "المشرف";
                    worksheet.Cells[2, 20].Value = "عدد الطلبات";
                    worksheet.Cells[2, 21].Value = "مكتملة";
                    worksheet.Cells[2, 22].Value = "مرفوضة";
                    worksheet.Cells[2, 23].Value = "تحت التنفيذ"; // إضافة عمود "تحت التنفيذ" في التقرير

                    row = 3;

                    string[] supervisors = {
    "مشرف خدمات الموظفين",
    "مشرف إدارة تخطيط الموارد البشرية",
    "مشرف إدارة تقنية المعلومات",
    "مشرف مراقبة الدوام",
    "مشرف السجلات الطبية",
    "مشرف إدارة الرواتب والاستحقاقات",
    "مشرف إدارة القانونية والالتزام",
    "مشرف خدمات الموارد البشرية",
    "مشرف إدارة الإسكان",
    "مشرف قسم الملفات",
    "مشرف العيادات الخارجية",
    "مشرف التأمينات الاجتماعية",
    "مشرف وحدة مراقبة المخزون",
    "مشرف إدارة تنمية الإيرادات",
    "مشرف إدارة الأمن و السلامة",
    "مشرف الطب الاتصالي"
};

                    foreach (var supervisor in supervisors)
                    {
                        string supervisorQuery = $"SELECT '{supervisor}' AS [المشرف], COUNT(*) AS [عدد الطلبات], " +
                                                 $"SUM(CASE WHEN [حالة الطلب] = 'مكتملة' THEN 1 ELSE 0 END) AS [مكتملة], " +
                                                 $"SUM(CASE WHEN [حالة الطلب] = 'مرفوضة' THEN 1 ELSE 0 END) AS [مرفوضة], " +
                                                 $"SUM(CASE WHEN [حالة الطلب] = 'تحت التنفيذ' THEN 1 ELSE 0 END) AS [تحت التنفيذ] " +  // حساب الطلبات تحت التنفيذ
                                                 $"FROM ordersTable WHERE [{supervisor}] IS NOT NULL";

                        SqlCommand supCmd = new SqlCommand(supervisorQuery, connection);
                        SqlDataReader supReader = supCmd.ExecuteReader();
                        while (supReader.Read())
                        {
                            worksheet.Cells[row, 19].Value = supReader["المشرف"];
                            worksheet.Cells[row, 20].Value = supReader["عدد الطلبات"];
                            worksheet.Cells[row, 21].Value = supReader["مكتملة"];
                            worksheet.Cells[row, 22].Value = supReader["مرفوضة"];
                            worksheet.Cells[row, 23].Value = supReader["تحت التنفيذ"];  // إضافة قيمة "تحت التنفيذ"
                            row++;
                        }
                        supReader.Close();
                    }

                    // إضافة عمود فارغ للفصل بين الجداول
                    row = 3;
                    worksheet.Cells[1, 23].Value = "";


                    // التقرير الخامس: التوزيع حسب نوع الطلبات
                    worksheet.Cells[1, 24].Value = "التوزيع حسب نوع الطلبات:";
                    worksheet.Cells[1, 24, 1, 25].Merge = true;
                    worksheet.Cells[1, 24].Style.Font.Bold = true;
                    worksheet.Cells[1, 24].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 24].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 24].Value = "نوع الطلب";
                    worksheet.Cells[2, 25].Value = "إجمالي الطلبات";

                    row = 3;

                    string requestTypeQuery = @"
            SELECT [نوع الطلب], COUNT(*) AS TotalRequests
            FROM ordersTable
            GROUP BY [نوع الطلب]";

                    SqlCommand requestTypeCmd = new SqlCommand(requestTypeQuery, connection);
                    SqlDataReader requestTypeReader = requestTypeCmd.ExecuteReader();
                    while (requestTypeReader.Read())
                    {
                        worksheet.Cells[row, 24].Value = requestTypeReader["نوع الطلب"];
                        worksheet.Cells[row, 25].Value = requestTypeReader["TotalRequests"];
                        row++;
                    }
                    requestTypeReader.Close();
                    // التقرير السادس: إجمالي الطلبات الشهرية
                    worksheet.Cells[1, 28].Value = "إجمالي الطلبات الشهرية:";
                    worksheet.Cells[1, 28, 1, 29].Merge = true;
                    worksheet.Cells[1, 28].Style.Font.Bold = true;
                    worksheet.Cells[1, 28].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, 28].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    worksheet.Cells[2, 28].Value = "الشهر";
                    worksheet.Cells[2, 29].Value = "إجمالي الطلبات";

                    row = 3;

                    string monthlyQuery = @"
                SELECT DATENAME(MONTH, [تاريخ الطلب]) AS [الشهر], COUNT(*) AS [إجمالي الطلبات]
                FROM ordersTable
                WHERE YEAR([تاريخ الطلب]) = YEAR(GETDATE())
                GROUP BY DATENAME(MONTH, [تاريخ الطلب]), MONTH([تاريخ الطلب])
                ORDER BY MONTH([تاريخ الطلب])";

                    SqlCommand monthlyCmd = new SqlCommand(monthlyQuery, connection);
                    SqlDataReader monthlyReader = monthlyCmd.ExecuteReader();
                    while (monthlyReader.Read())
                    {
                        worksheet.Cells[row, 28].Value = monthlyReader["الشهر"];
                        worksheet.Cells[row, 29].Value = monthlyReader["إجمالي الطلبات"];
                        row++;
                    }
                    monthlyReader.Close();
                    // تنسيق الأعمدة لتلائم المحتوى
                    worksheet.Cells[1, 1, row - 1, 29].AutoFitColumns();

                    // تصدير الملف
                    var fileContents = package.GetAsByteArray();

                    Response.Clear();
                    Response.Buffer = true;
                    Response.AddHeader("content-disposition", "attachment;filename=DetailedStatisticsExport.xlsx");
                    Response.Charset = "";
                    Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    Response.BinaryWrite(fileContents);
                    Response.Flush();
                    Response.End();
                }
            }
        }
        private void AddAttachmentColumns()
        {
            // إضافة الأعمدة الخاصة بالملفات المرفقة إلى GridView1
            for (int i = 1; i <= 4; i++)
            {
                TemplateField attachmentField = new TemplateField();
                attachmentField.HeaderText = $"ملف {i}";
                attachmentField.ItemTemplate = new AttachmentTemplate(i);
                GridView1.Columns.Add(attachmentField);
            }
        }

        // قالب مخصص لعرض الملفات المرفقة
        public class AttachmentTemplate : ITemplate
        {
            private readonly int fileNumber;

            public AttachmentTemplate(int fileNumber)
            {
                this.fileNumber = fileNumber;
            }

            public void InstantiateIn(Control container)
            {
                // إضافة Panel للتحكم في العرض
                Panel buttonPanel = new Panel
                {
                    ID = $"pnlButtons_{fileNumber}",
                    CssClass = "d-flex gap-2"
                };

                // زر التحميل
                LinkButton downloadButton = new LinkButton
                {
                    ID = $"lnkDownload_{fileNumber}",
                    Text = "تحميل",
                    CommandName = "DownloadFile",
                    CommandArgument = fileNumber.ToString(),
                    CssClass = "btn btn-primary btn-sm"
                };
                downloadButton.Click += new EventHandler(DownloadFile_Click);
                buttonPanel.Controls.Add(downloadButton);

                // زر الحذف
                LinkButton deleteButton = new LinkButton
                {
                    ID = $"lnkDelete_{fileNumber}",
                    Text = "حذف",
                    CommandName = "DeleteFile",
                    CommandArgument = fileNumber.ToString(),
                    CssClass = "btn btn-danger btn-sm ms-2"
                };
                deleteButton.Click += new EventHandler(DeleteFile_Click);
                buttonPanel.Controls.Add(deleteButton);

                // إضافة Panel إلى الحاوية
                container.Controls.Add(buttonPanel);
            }

            // دالة التحميل
            protected void DownloadFile_Click(object sender, EventArgs e)
            {
                try
                {
                    LinkButton btn = (LinkButton)sender;
                    GridViewRow row = (GridViewRow)btn.NamingContainer;
                    string orderNumber = row.Cells[0].Text.Trim();

                    string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                    using (SqlConnection con = new SqlConnection(connectionString))
                    {
                        string query = @"SELECT [ملف1], [ملف2], [ملف3], [ملف4], [اسم الموظف]
                           FROM ordersTable 
                           WHERE [رقم الطلب] = @OrderNumber";

                        using (SqlCommand cmd = new SqlCommand(query, con))
                        {
                            cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                            con.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    string employeeName = reader["اسم الموظف"].ToString().Trim();
                                    byte[] compressedData = reader[fileNumber - 1] as byte[];

                                    if (compressedData != null && compressedData.Length > 0)
                                    {
                                        try
                                        {
                                            byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                            if (pdfData != null && IsPdfFile(pdfData))
                                            {
                                                string fileName = CleanFileName($"مرفق_{fileNumber}_طلب_{orderNumber}_{employeeName}.pdf");
                                                System.Diagnostics.Debug.WriteLine($"✅ جاري تحميل الملف: {fileName}");

                                                var response = HttpContext.Current.Response;
                                                response.Clear();
                                                response.ContentType = "application/pdf";
                                                response.AddHeader("Content-Disposition", $"attachment; filename={HttpUtility.UrlEncode(fileName)}");
                                                response.OutputStream.Write(pdfData, 0, pdfData.Length);
                                                response.Flush();
                                                response.End();
                                            }
                                            else
                                            {
                                                System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                                                ShowAlert("الملف غير صالح أو تالف.");
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في فك ضغط الملف: {ex.Message}");
                                            ShowAlert("حدث خطأ أثناء معالجة الملف.");
                                        }
                                    }
                                    else
                                    {
                                        ShowAlert("الملف غير متوفر.");
                                    }
                                }
                                else
                                {
                                    ShowAlert("لم يتم العثور على الطلب.");
                                }
                            }
                        }
                    }
                }
                catch (System.Threading.ThreadAbortException)
                {
                    // تجاهل هذا الخطأ لأنه متوقع عند استخدام Response.End()
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ: {ex.Message}");
                    ShowAlert("حدث خطأ أثناء تحميل الملف.");
                }
            }

            private string CleanFileName(string fileName)
            {
                // إزالة الأحرف غير المسموح بها في أسماء الملفات
                char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
                foreach (char c in invalidChars)
                {
                    fileName = fileName.Replace(c, '_');
                }

                // التأكد من أن الاسم لا يتجاوز طولاً معيناً
                const int maxLength = 100;
                if (fileName.Length > maxLength)
                {
                    string extension = System.IO.Path.GetExtension(fileName);
                    fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
                }

                return fileName;
            }
            // دالة عرض الرسائل
            private void ShowAlert(string message)
            {
                Page page = HttpContext.Current.Handler as Page;
                if (page != null)
                {
                    string script = $@"
            Swal.fire({{
                title: 'تنبيه',
                text: '{message}',
                icon: 'warning',
                confirmButtonText: 'حسناً'
            }});";

                    ScriptManager.RegisterStartupScript(page, page.GetType(),
                        "alert_" + Guid.NewGuid().ToString(), script, true);
                }
            }




            private bool ValidatePdfFormat(byte[] fileData)
            {
                try
                {
                    if (fileData == null || fileData.Length < 4)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                        return false;
                    }

                    bool isPdf = fileData[0] == 0x25 && // %
                                fileData[1] == 0x50 && // P
                                fileData[2] == 0x44 && // D
                                fileData[3] == 0x46;   // F

                    if (!isPdf)
                    {
                        System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                    }

                    return isPdf;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                    return false;
                }
            }




            // دالة الحذف
            protected void DeleteFile_Click(object sender, EventArgs e)
            {
                try
                {
                    LinkButton btn = (LinkButton)sender;
                    GridViewRow row = (GridViewRow)btn.NamingContainer;
                    string orderNumber = row.Cells[0].Text.Trim();

                    if (string.IsNullOrWhiteSpace(orderNumber))
                    {
                        ShowAlert("رقم الطلب غير متوفر.");
                        return;
                    }

                    string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                    using (SqlConnection con = new SqlConnection(connectionString))
                    {
                        string query = $"UPDATE ordersTable SET [ملف{fileNumber}] = NULL WHERE [رقم الطلب] = @OrderNumber";
                        using (SqlCommand cmd = new SqlCommand(query, con))
                        {
                            cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                            con.Open();

                            int rowsAffected = cmd.ExecuteNonQuery();
                            if (rowsAffected > 0)
                            {
                                ShowAlert("تم حذف الملف بنجاح");
                            }
                            else
                            {
                                ShowAlert("لم يتم العثور على الملف للحذف");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ShowAlert("حدث خطأ أثناء حذف الملف: " + ex.Message);
                }
            }



            // التحقق من أن الملف بصيغة PDF
            /// <summary>
            /// التحقق من أن الملف هو PDF صالح عن طريق فحص توقيع الملف
            /// </summary>
            /// <param name="fileData">محتوى الملف كمصفوفة بايت</param>
            /// <returns>true إذا كان الملف PDF صالح، false في حال العكس</returns>
            private bool IsPdfFile(byte[] data)
            {
                if (data == null || data.Length < 4)
                    return false;

                // التحقق من PDF header
                return data[0] == 0x25 && // %
                       data[1] == 0x50 && // P
                       data[2] == 0x44 && // D
                       data[3] == 0x46;   // F
            }



        }




    }

}






