using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IAutoRoutingRepository
    {
        Task<List<AutoRouting>> GetAllAsync();
        Task<AutoRouting?> GetByIdAsync(int id);
        Task<AutoRouting> AddAsync(AutoRouting entity);
        Task UpdateAsync(AutoRouting entity);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(string orderType, string nationality, string job, int? excludeId = null);
        Task<int> GetActiveCountAsync();
        Task<List<AutoRouting>> GetActiveRoutesAsync();
        Task<AutoRouting?> GetMatchingRouteAsync(string orderType, string nationality, string job);
    }
}
