using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IAssistantManagerOrderService
{
    Task<ServiceResult<List<OrderSummaryDto>>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId);
    Task<ServiceResult> ConfirmOrderByAssistantManagerAsync(int orderId, string? userName);
    Task<ServiceResult> ReturnOrderToDirectManagerAsync(int orderId, string reason, string? userName);
    Task<ServiceResult> RejectOrderByAssistantManagerAsync(int orderId, string reason, string? userName);
}
