@* Manual Routing Partial *@
@model OrderFlowCore.Web.ViewModels.PathManagementDashboardViewModel
<div class="row">
    <!-- Path Cards Grid -->
    <div class="row g-3">
        @for (int i = 1; i <= 6; i++)
        {
            var pathKey = $"مسار{i}";
            var pathSupervisors = Model.PathsConfiguration.ContainsKey(pathKey)
                ? Model.PathsConfiguration[pathKey] : new List<string>();
            <div class="col-md-4">
                <div class="card-custom">
                    <div class="card-header-custom d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">@pathKey</h5>
                        <span class="badge bg-light text-dark">@pathSupervisors.Count مشرفين</span>
                    </div>
                    <div class="card-body">
                        <div class="supervisor-grid">
                            @foreach (var supervisor in Model.AvailableSupervisors)
                            {
                                var isChecked = pathSupervisors.Contains(supervisor);
                                <div class="supervisor-card @(isChecked ? "selected" : "")">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" @(isChecked ? "checked" : "") data-path="@pathKey" data-supervisor="@supervisor">
                                        <label class="form-check-label">@supervisor</label>
                                    </div>
                                </div>
                            }
                        </div>
                        <button class="btn btn-sm btn-primary-custom w-100 mt-3 btn-save-manual-paths">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
</div> 