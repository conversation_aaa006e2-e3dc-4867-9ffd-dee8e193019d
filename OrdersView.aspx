<%@ Page Title="عرض الطلبات" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="OrdersView.aspx.cs" Inherits="abozyad.WebForm3" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">


    <style>
               /* العمود الاول إحصائيات العامة */
        .stat-box {
   background: #fff;
   transition: all 0.3s ease;
}

.stat-box:hover {
   transform: translateY(-3px);
   box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-label {
   font-size: 0.9rem;
   color: #6c757d;
   margin-bottom: 0.5rem;
}

.stat-value {
   font-size: 1.5rem;
   font-weight: bold;
}

.table td {
   vertical-align: middle;
   padding: 0.75rem;
}


/* تنسيق جدول توزيع الطلبات */
.stages-stats .table td {
    vertical-align: middle;
    padding: 12px 15px;
}

/* تنسيق الأرقام في الجدول */
.stages-stats .table td:last-child {
    font-size: 1.5rem;  /* تكبير حجم الأرقام */
    font-weight: bold;
    text-align: center;
    min-width: 80px;
}

/* تنسيق عناوين الصفوف */
.stages-stats .table td strong {
    font-size: 1.1rem;
    color: #333;
}

/* إضافة تأثير عند المرور */
.stages-stats .table tr:hover {
    background-color: #f8f9fa;
}
/* نهاية العمود الاول */







/* نهاية التبويبات */


/* التبويبات */
/* التبويبات */
.nav-tabs .nav-link {
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6 #dee2e6 #fff;
    margin-right: 2px;
    transition: all 0.3s ease;
    font-size: 1rem;
    padding: 0.75rem 1.25rem;
}

.nav-tabs .nav-link:hover {
    background-color: #e9ecef;
    transform: translateY(-2px); /* تأثير إضافي */
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-bottom-color: transparent;
    font-weight: bold;
    transform: translateY(-2px); /* تأثير إضافي */
}

.details-box, .stats-details {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 10px;
}

.summary-box {
    border-right: 4px solid #007bff;
    border-radius: 4px;
}

/* البطاقات */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 1rem;
}

.card-header {
    border-bottom: 2px solid rgba(0,0,0,0.125);
}

/* وسائل الإعلام */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tabs .nav-item {
        width: 100%;
        margin-bottom: 0.25rem;
    }
    
    .nav-link {
        border-radius: 4px;
    }
}

/* تنسيق الجدول */
table {
    width: 100%;
    border-collapse: collapse;
    text-align:  right;
}

/* حدود الجدول */
table, th, td {
    border: 1px solid #dee2e6;
}

/* تنسيق الرؤوس */
th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
    padding: 8px;
    font-size: 14px;
    color: #495057;
}

/* تنسيق الصفوف */
td {
    padding: 8px;
    font-size: 13px;
}

/* تحسين العرض التلقائي */
th, td {
    white-space: nowrap; /* يجعل النص في سطر واحد */
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تأثير التمرير على الصفوف */
tr:hover {
    background-color: #f1f3f5;
}

/* ألوان الخلفية للصفوف المتناوبة */
tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:nth-child(odd) {
    background-color: #ffffff;
}

/* تحسين العرض عند الأجهزة الصغيرة */
@media (max-width: 768px) {
    table {
        font-size: 12px;
    }
    th, td {
        padding: 5px;
    }
}

/* تنسيق القسم الرئيسي */
.department-stats {
    font-family: Arial, sans-serif;
}

.department-row {
    padding: 10px;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* تنسيق العناوين */
.department-row strong {
    color: #333;
    margin-right: 10px;
}

/* تنسيق القيم حسب العدد */
.value-0 {
    color: #999;  /* رمادي للقيم الصفرية */
}

.value-1-3 {
    color: #28a745;  /* أخضر فاتح للقيم من 1-3 */
}

.value-4-7 {
    color: #ffc107;  /* أصفر فاتح للقيم من 4-7 */
}

.value-8-10 {
    color: #fd7e14;  /* برتقالي فاتح للقيم من 8-10 */
}

.value-more-than-10 {
    color: #dc3545;  /* أحمر غامق للقيم الأكبر من 10 */
}

/* تنسيق الإحصائيات الإجمالية */
.summary-stats {
    background-color: #f8f9fa;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.summary-stats strong {
    margin: 0 10px;
}

.under-execution {
    color: #007bff;
}

.completed {
    color: #28a745;
}

.avg-completion-time {
    color: #6c757d;
}
/* ألوان إضافية للعناوين */
.bg-purple {
    background-color: #6f42c1 !important;
}

.bg-indigo {
    background-color: #ff9f9f !important;
}

.bg-teal {
    background-color: #20c997 !important;
}

/* تأثير التحويم للعناوين */
.card-header {
    transition: all 0.3s ease;
}

.card-header:hover {
    filter: brightness(110%);
}

/* تخصيص حجم مربع التاريخ */
input[type="date"] {
    font-size: 16px; /* تكبير النص داخل المربع */
    padding: 8px; /* مساحة داخلية للمربع */
    border: 1px solid #ccc; /* حدود خفيفة */
    border-radius: 6px; /* زوايا مستديرة */
    width: 100%; /* عرض كامل */
}

/* تحسين مظهر زر التقويم */
input[type="date"]::-webkit-calendar-picker-indicator {
    width: 24px; /* تكبير أيقونة التقويم */
    height: 24px;
    cursor: pointer;
}

/* تحسين الشكل داخل الأعمدة */
.form-control {
    max-width: 100%; /* ضمان التناسب في الأعمدة */
}

.pending-requests {
    border-top: 1px dashed #ccc;
    margin-top: 20px;
    padding-top: 15px;
    opacity: 0.8;
}

.coordinator-row {
    background-color: #fff;
}
    </style>









    <script type="text/javascript">
function confirmDelete() {
    return confirm('هل أنت متأكد من حذف الطلبات القديمة؟ لا يمكن التراجع عن هذا الإجراء.');
}
    </script>







   
   <!-- قسم التصفية المحسن -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h3 class="card-title mb-0">خيارات التصفية والإجراءات</h3>
    </div>
    <div class="card-body">
        <!-- مجموعة التصفية الأساسية -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="ddlStatusFilter" class="form-label">حالة الطلب</label>
                    <asp:DropDownList ID="ddlStatusFilter" runat="server" 
                        CssClass="form-select" 
                        AutoPostBack="True" 
                        OnSelectedIndexChanged="ddlStatusFilter_SelectedIndexChanged">
                        <asp:ListItem Text="كل الحالات" Value="All"></asp:ListItem>
                    </asp:DropDownList>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="ddlDepartmentFilter" class="form-label">القسم</label>
                    <asp:DropDownList ID="ddlDepartmentFilter" runat="server" 
                        CssClass="form-select" 
                        AutoPostBack="True" 
                        OnSelectedIndexChanged="ddlDepartmentFilter_SelectedIndexChanged">
                        <asp:ListItem Text="كل الأقسام" Value="All"></asp:ListItem>
                    </asp:DropDownList>
                </div>
            </div>
            <div class="col-md-3">
    <div class="form-group">
        <label for="txtFromDate" class="form-label">من تاريخ</label>
        <asp:TextBox ID="txtFromDate" runat="server" 
            CssClass="form-control" 
            TextMode="Date">
        </asp:TextBox>
    </div>
</div>
<div class="col-md-3">
    <div class="form-group">
        <label for="txtToDate" class="form-label">إلى تاريخ</label>
        <asp:TextBox ID="txtToDate" runat="server" 
            CssClass="form-control" 
            TextMode="Date">
        </asp:TextBox>
    </div>
</div>

        </div>

        <!-- مجموعة السجل المدني -->
        <div class="row mb-3">
<div class="col-md-6">
    <div class="input-group">
        <span class="input-group-text">السجل المدني</span>
        <asp:TextBox ID="txtCivilRecord" runat="server" 
            CssClass="form-control" 
            placeholder="أدخل السجل المدني">
        </asp:TextBox>
        <asp:Button ID="btnFilterByCivilRecord" runat="server" 
            Text="بحث" 
            CssClass="btn btn-primary" 
            OnClick="btnFilterByCivilRecord_Click" />
        <asp:Button ID="btnExportFiltered" runat="server"
            Text="تصدير النتائج" 
            CssClass="btn btn-success"
            OnClick="ExportFilteredResultsToExcel" />
    </div>
</div>
            <div class="col-md-6">
                <div class="d-flex gap-2">
                    <asp:Button ID="btnFilterByDate" runat="server" 
                        Text="تصفية التاريخ" 
                        CssClass="btn btn-primary" 
                        OnClick="btnFilterByDate_Click" />
                    <asp:Button ID="btnClearFilters" runat="server" 
                        Text="إلغاء التصفية" 
                        CssClass="btn btn-outline-secondary" 
                        OnClick="btnClearFilters_Click" 
                        OnClientClick="return confirm('هل أنت متأكد من إلغاء جميع التصفية؟');" />
                </div>
            </div>
        </div>

        <hr class="my-4">

        <!-- مجموعة أزرار التصدير والتقارير -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex gap-2 flex-wrap">
                    <asp:Button ID="btnExportToExcel" runat="server" 
                        Text="تصدير الطلبات" 
                        CssClass="btn btn-success" 
                        OnClick="btnExportToExcel_Click" />
                    <asp:Button ID="btnExportDetailedStatisticsToExcel" runat="server" 
                        Text="تصدير التقارير المفصلة" 
                        CssClass="btn btn-info" 
                        OnClick="btnExportDetailedStatisticsToExcel_Click" />
                    <asp:Button ID="btnGenerateReports" runat="server" 
                        Text="توليد التقارير" 
                        CssClass="btn btn-secondary" 
                        OnClick="GenerateReportsButton_Click" />
                </div>
            </div>
        </div>

<!-- قسم حذف الطلبات -->
<div class="row">
    <div class="col-12">
        <!-- حذف الطلبات القديمة -->
        <div class="card bg-light mb-4">
            <div class="card-body">
                <h4 class="card-title mb-3">حذف الطلبات القديمة</h4>
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="ddlDeletePeriod" class="form-label">اختر الفترة</label>
                            <asp:DropDownList ID="ddlDeletePeriod" runat="server" 
                                CssClass="form-select" 
                                AutoPostBack="true" 
                                OnSelectedIndexChanged="ddlDeletePeriod_SelectedIndexChanged">
                                <asp:ListItem Value="3" Text="3 أشهر"></asp:ListItem>
                                <asp:ListItem Value="6" Text="6 أشهر"></asp:ListItem>
                                <asp:ListItem Value="9" Text="9 أشهر"></asp:ListItem>
                                <asp:ListItem Value="12" Text="سنة واحدة" Selected="True"></asp:ListItem>
                                <asp:ListItem Value="24" Text="سنتين"></asp:ListItem>
                                <asp:ListItem Value="36" Text="3 سنوات"></asp:ListItem>
                                <asp:ListItem Value="-1" Text="تحديد نطاق تاريخي"></asp:ListItem>
                            </asp:DropDownList>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <asp:Panel ID="pnlDateRange" runat="server" Visible="false">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="txtStartDate" class="form-label">من تاريخ</label>
                                        <asp:TextBox ID="txtStartDate" runat="server" 
                                            TextMode="Date" 
                                            CssClass="form-control">
                                        </asp:TextBox>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="txtEndDate" class="form-label">إلى تاريخ</label>
                                        <asp:TextBox ID="txtEndDate" runat="server" 
                                            TextMode="Date" 
                                            CssClass="form-control">
                                        </asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                    </div>
                    <div class="col-md-3">
                        <asp:Button ID="btnDeleteOldOrders" runat="server"
                            Text="حذف الطلبات"
                            CssClass="btn btn-danger w-100"
                            OnClick="btnDeleteOldOrders_Click"
                            OnClientClick="return confirm('هل أنت متأكد من حذف الطلبات القديمة؟ لا يمكن التراجع عن هذا الإجراء.');" />
                    </div>
                </div>
            </div>
        </div>

<!-- قسم حذف طلب محدد -->
<div class="card bg-light">
    <div class="card-body">
        <h4 class="card-title mb-3">حذف طلب محدد</h4>
        <div class="row align-items-end">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="ddlAllOrderNumbers" class="form-label">رقم الطلب</label>
                    <asp:DropDownList ID="ddlAllOrderNumbers" runat="server" 
                        CssClass="form-select">
                    </asp:DropDownList>
                </div>
            </div>
            <div class="col-md-4">
                <asp:Button ID="btnDeleteOrder" runat="server" 
                    Text="حذف الطلب" 
                    CssClass="btn btn-danger w-100"
                    OnClick="btnDeleteOrder_Click"
                    OnClientClick="return confirm('هل أنت متأكد من حذف هذا الطلب؟');" />
            </div>
        </div>
        <div class="mt-3">
            <asp:Label ID="LabelMessage" runat="server" CssClass="alert alert-success d-block" Visible="false" />
            <asp:Label ID="LabelError" runat="server" CssClass="alert alert-danger d-block" Visible="false" />
        </div>
    </div>
</div>
    </div>
</div>
</div>
    </div>










       <!-- العمود الأول: إحصائيات عامة -->
<div class="card mb-4">
   <div class="card-header bg-primary text-white">
       <h3 class="card-title mb-0">إحصائيات عامة</h3>
   </div>
   <div class="card-body">
       <!-- إحصائيات إجمالية -->
       <div class="summary-stats mb-4">
           <div class="row g-3">
               <div class="col-md-6 col-lg-3">
                   <div class="stat-box border rounded p-3 text-center">
                       <div class="stat-label">إجمالي الطلبات</div>
                       <div class="stat-value text-primary">
                           <asp:Label ID="lblTotalRequests" runat="server" Text="0"></asp:Label>
                       </div>
                   </div>
               </div>
               <div class="col-md-6 col-lg-3">
                   <div class="stat-box border rounded p-3 text-center">
                       <div class="stat-label">الطلبات المنجزة</div>
                       <div class="stat-value text-success">
                           <asp:Label ID="lblCompletedRequests" runat="server" Text="0"></asp:Label>
                       </div>
                   </div>
               </div>
               <div class="col-md-6 col-lg-3">
                   <div class="stat-box border rounded p-3 text-center">
                       <div class="stat-label">تحت التنفيذ</div>
                       <div class="stat-value text-warning">
                           <asp:Label ID="lblPendingRequests" runat="server" Text="0"></asp:Label>
                       </div>
                   </div>
               </div>
               <div class="col-md-6 col-lg-3">
                   <div class="stat-box border rounded p-3 text-center">
                       <div class="stat-label">متوسط زمن الإنجاز</div>
                       <div class="stat-value text-info">
                           <asp:Label ID="lblAvgTimeToComplete" runat="server" Text="0"></asp:Label>
                           
                       </div>
                   </div>
               </div>
           </div>
       </div>

       <!-- إحصائيات المراحل -->
       <div class="stages-stats">
           <h4 class="mb-3">توزيع الطلبات حسب المراحل</h4>
           <div class="table-responsive">
               <table class="table table-hover border">
                   <tbody>
                       <tr>
                           <td><strong>مدير القسم</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsDM" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>مساعد المدير للخدمات الطبية</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsA1" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>مساعد المدير لخدمات التمريض</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsA2" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>مساعد المدير للخدمات الإدارية</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsA3" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>مساعد المدير للموارد البشرية</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsA4" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>منسق الموارد البشرية</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsB" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>المشرفين</strong></td>
                           <td class="stage-value"><asp:Literal ID="lblRequestsC" runat="server"></asp:Literal></td>
                       </tr>
                       <tr>
                           <td><strong>مدير الموارد البشرية</strong></td>
                          <td class="stage-value"><asp:Literal ID="lblRequestsD" runat="server"></asp:Literal></td>
                       </tr>
                   </tbody>
               </table>
           </div>
       </div>
   </div>
</div>


<div class="card">
    <div class="card-header bg-light">
        <ul class="nav nav-tabs card-header-tabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#departmentStats">
                    <i class="fas fa-building ml-1"></i>
                    إحصائيات الأقسام
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#assistants">
                    <i class="fas fa-user-tie ml-1"></i>
                    إحصائيات المساعدين
                </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="tab" href="#coordinatorStats">
                <i class="fas fa-user-tie ml-1"></i>
                  إحصائيات المنسقين
                </a>
             </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#supervisors">
                    <i class="fas fa-users-cog ml-1"></i>
                    إحصائيات المشرفين
                </a>
            </li>
          <li class="nav-item">
              <a class="nav-link" data-bs-toggle="tab" href="#hr-manager">
                  <i class="fas fa-user-tie ml-1"></i>
                 إحصائيات المدير 
              </a>
           </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#completedTasks">
                    <i class="fas fa-check-circle ml-1"></i>
                    الطلبات المنجزة
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#monthlyStats">
                    <i class="fas fa-calendar-alt ml-1"></i>
                    إحصائيات شهرية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#transferTypes">
                    <i class="fas fa-exchange-alt ml-1"></i>
                    نوع التحويل
                </a>
            </li>
        </ul>
    </div>

    <div class="card-body">
        <div class="tab-content">
            <!-- تبويب إحصائيات الأقسام -->
            <div id="departmentStats" class="tab-pane fade show active">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">إحصائيات الأقسام التفصيلية</h3>
                    </div>
                    <div class="card-body">
                        <div class="summary-stats bg-light p-3 rounded mb-4">
                            <asp:Literal ID="DepartmentSummaryStatistics" runat="server"></asp:Literal>
                        </div>
                        <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                            <asp:Literal ID="DepartmentStatisticsColumn1" runat="server"></asp:Literal>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب إحصائيات المساعدين -->
<div id="assistants" class="tab-pane fade">
    <div class="card h-100">
        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">إحصائيات المساعدين التفصيلية</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="AssistantSummaryStatistics" runat="server"></asp:Literal>
            </div>
            <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="AssistantStatistics" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>
                        <!-- تبويب منسقي الموارد البشرية -->
<div id="coordinatorStats" class="tab-pane fade">
    <div class="card h-100">
        <div class="card-header bg-purple text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">إحصائيات الطلبات المنجزة حسب منسقي الموارد البشرية</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="CoordinatorSummaryStatistics" runat="server"></asp:Literal>
            </div>
            <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="CoordinatorStatisticsNames" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>


            <!-- تبويب إحصائيات المشرفين -->
<!-- تبويب إحصائيات المشرفين -->
<div id="supervisors" class="tab-pane fade">
    <div class="card h-100">
        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">إحصائيات المشرفين التفصيلية</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="SupervisorSummaryStatistics" runat="server"></asp:Literal>
            </div>
            <div class="supervisor-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="SupervisorStatistics" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>
            <!-- تبويب الطلبات المنجزة -->
<div id="completedTasks" class="tab-pane fade">
    <div class="card h-100">
        <div class="card-header bg-purple text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">إحصائيات الطلبات المنجزة حسب أسماء المشرفين</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="CompletedTasksSummary" runat="server"></asp:Literal>
            </div>
            <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="SupervisorStatisticsNames" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>
            <!-- تبويب إحصائيات مدير الموارد البشرية -->
<div id="hr-manager" class="tab-pane fade">
    <div class="card h-100">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">إحصائيات المدير التفصيلية</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="HRManagerSummaryStatistics" runat="server"></asp:Literal>
            </div>
            <div class="hr-manager-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="HRManagerStatistics" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>
            <!-- تبويب الإحصائيات الشهرية -->
<div id="monthlyStats" class="tab-pane fade">
    <div class="card h-100">
        <div class="card-header bg-indigo text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">الإحصائيات الشهرية</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="MonthlySummaryStats" runat="server"></asp:Literal>
            </div>
            <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="MonthlyTotalsLiteral" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>

            <!-- تبويب نوع التحويل -->
<div id="transferTypes" class="tab-pane fade">
    <div class="card h-100">
       <div class="card-header bg-teal text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">إحصائيات نوع التحويل</h3>
        </div>
        <div class="card-body">
            <div class="summary-stats bg-light p-3 rounded mb-4">
                <asp:Literal ID="TransferTypeSummary" runat="server"></asp:Literal>
            </div>
            <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                <asp:Literal ID="trasnferTypeLiteral" runat="server"></asp:Literal>
            </div>
        </div>
    </div>
</div>
    </div>
 </div>
     </div>

        









   







    <!-- GridView لعرض البيانات -->
    <asp:GridView ID="GridView1" runat="server" CssClass="grid-view" AutoGenerateColumns="False" AllowSorting="True" OnSorting="GridView1_Sorting" OnRowDataBound="GridView1_RowDataBound" AllowPaging="False"></asp:GridView>
    <!-- رسائل الخطأ -->
    <asp:Label ID="lblErrorMessage" runat="server" CssClass="error-message" ForeColor="Red" />
    <!-- رسائل الخطأ في النهاية -->
    <asp:Label ID="lblMessage" runat="server" ForeColor="Red" CssClass="error-message"></asp:Label>
</asp:Content>
