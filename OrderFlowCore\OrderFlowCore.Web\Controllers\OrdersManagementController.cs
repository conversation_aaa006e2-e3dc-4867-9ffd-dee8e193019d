using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using System.Security.Claims;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class OrdersManagementController : Controller
    {
        private readonly IOrdersManagementService _ordersManagementService;
        private readonly ILogger<OrdersManagementController> _logger;

        public OrdersManagementController(
            IOrdersManagementService ordersManagementService,
            ILogger<OrdersManagementController> logger)
        {
            _ordersManagementService = ordersManagementService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new OrdersManagementViewModel();

            try
            {
                // Check if user is admin
                viewModel.IsAdmin = IsUserAdmin();

                // Load dropdown options
                await LoadDropdownOptionsAsync(viewModel);

                // Load initial orders (all orders)
                var ordersResult = await _ordersManagementService.GetAllOrdersAsync();
                if (ordersResult.IsSuccess)
                {
                    viewModel.Orders = ordersResult.Data;
                    viewModel.TotalOrdersCount = ordersResult.Data.Count;
                }
                else
                {
                    viewModel.ErrorMessage = ordersResult.Message;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل صفحة إدارة الطلبات");
                viewModel.ErrorMessage = "حدث خطأ أثناء تحميل الصفحة";
            }

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> FilterOrders(OrdersManagementViewModel model)
        {
            try
            {
                model.IsAdmin = IsUserAdmin();
                await LoadDropdownOptionsAsync(model);

                var filterDto = model.GetFilterDto();
                var ordersResult = await _ordersManagementService.GetFilteredOrdersAsync(filterDto);

                if (ordersResult.IsSuccess)
                {
                    model.Orders = ordersResult.Data;
                    model.TotalOrdersCount = ordersResult.Data.Count;
                    model.SuccessMessage = $"تم العثور على {ordersResult.Data.Count} طلب";
                }
                else
                {
                    model.ErrorMessage = ordersResult.Message;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصفية الطلبات");
                model.ErrorMessage = "حدث خطأ أثناء تصفية الطلبات";
            }

            return View("Index", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ClearFilters()
        {
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportOrders(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Forbid();
                }

                var filterDto = model.GetFilterDto();
                var exportResult = await _ordersManagementService.ExportOrdersToExcelAsync(filterDto);

                if (exportResult.IsSuccess)
                {
                    var fileName = $"الطلبات_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = exportResult.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الطلبات");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير الطلبات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportFilteredResults(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Forbid();
                }

                var filterDto = model.GetFilterDto();
                var exportResult = await _ordersManagementService.ExportFilteredResultsToExcelAsync(filterDto);

                if (exportResult.IsSuccess)
                {
                    var fileName = $"النتائج_المفلترة_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = exportResult.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير النتائج المفلترة");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير النتائج";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportDetailedStatistics()
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Forbid();
                }

                var exportResult = await _ordersManagementService.ExportDetailedStatisticsToExcelAsync();

                if (exportResult.IsSuccess)
                {
                    var fileName = $"التقارير_المفصلة_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = exportResult.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير التقارير المفصلة");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير التقارير";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteOldOrders(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var deleteDto = model.GetDeleteOldOrdersDto();
                var result = await _ordersManagementService.DeleteOldOrdersAsync(deleteDto, userName);

                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلبات القديمة");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الطلبات القديمة" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteSpecificOrder(int orderId)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var result = await _ordersManagementService.DeleteSpecificOrderAsync(orderId, userName);

                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلب {OrderId}", orderId);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الطلب" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> GenerateReports()
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var result = await _ordersManagementService.GenerateReportsAsync(userName);

                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد التقارير");
                return Json(new { success = false, message = "حدث خطأ أثناء توليد التقارير" });
            }
        }

        private async Task LoadDropdownOptionsAsync(OrdersManagementViewModel viewModel)
        {
            // Load status options
            var statusResult = await _ordersManagementService.GetOrderStatusesAsync();
            if (statusResult.IsSuccess)
            {
                viewModel.StatusOptions = statusResult.Data.Select(s => new SelectListItem
                {
                    Value = s.Value,
                    Text = s.Text
                }).ToList();
            }

            // Load department options
            var departmentResult = await _ordersManagementService.GetDepartmentsAsync();
            if (departmentResult.IsSuccess)
            {
                viewModel.DepartmentOptions = departmentResult.Data.Select(d => new SelectListItem
                {
                    Value = d.Value,
                    Text = d.Text
                }).ToList();
            }

            // Load order numbers for deletion
            var orderNumbersResult = await _ordersManagementService.GetOrderNumbersAsync();
            if (orderNumbersResult.IsSuccess)
            {
                viewModel.OrderNumbers = orderNumbersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Value,
                    Text = o.Text
                }).ToList();
            }
        }

        private bool IsUserAdmin()
        {
            // Check if user has admin role - adjust based on your role system
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            return userRole == "مدير حسابات" || userRole == "مدير الموارد البشرية" || userRole == "Admin";
        }
    }
}
