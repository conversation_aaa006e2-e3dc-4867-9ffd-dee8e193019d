// HR Coordinator JavaScript Module
const HRCoordinator = {
    // Global state
    currentOrderId: null,
    currentRestoreOrderId: null,
    modals: {},

    // Initialize the module based on current page
    init: function () {
        this.detectPageType();
        this.initializeModals();
        this.bindEvents();
        this.showTempDataMessages();
    },

    // Detect which page we're on and initialize accordingly
    detectPageType: function () {
        const path = window.location.pathname.toLowerCase();

        if (path.includes('/hrcoordinator/processorder/')) { 
            this.initProcessOrderPage();
        } else if (path.includes('/hrcoordinator/restore')) { 
            this.initRestorePage();
        } else {
            this.initIndexPage();
        }
    },

    // Initialize Index page functionality
    initIndexPage: function () {
        // Index page is already handled by the inline script
        // This is just for any additional initialization
    },

    // Initialize ProcessOrder page functionality
    initProcessOrderPage: function () {
        // Extract order ID from URL
        const pathParts = window.location.pathname.split('/');
        const orderId = pathParts[pathParts.length - 1];

        if (orderId && !isNaN(orderId)) {
            this.currentOrderId = parseInt(orderId);
            document.getElementById('orderNumber').textContent = this.currentOrderId;
            this.loadOrderDetails(this.currentOrderId);
        }
    },

    // Initialize Restore page functionality
    initRestorePage: function () {
        // Load initial restorable orders
        this.searchRestorableOrders();
    },

    // Initialize Bootstrap modals
    initializeModals: function () {
        const modalElements = [
            'confirmSubmitModal',
            'confirmAutoPathModal',
            'confirmDirectToManagerModal',
            'confirmReturnModal',
            'confirmRejectModal',
            'confirmRestoreModal',
            'confirmActionRequiredModal',
        ];

        modalElements.forEach(modalId => {
            const element = document.getElementById(modalId);
            if (element) {
                this.modals[modalId] = new bootstrap.Modal(element);
            }
        });
    },

    // Bind event handlers
    bindEvents: function () {
        // Order selection change handler (Index page)
        const orderSelect = document.getElementById('orderSelect');
        if (orderSelect) {
            orderSelect.addEventListener('change', (e) => {
                const orderId = e.target.value;
                this.loadOrderDetails(orderId);
            });
        }

        // btnProcessOrder click handler (Index page)
        const btnProcessOrder = document.getElementById('btnProcessOrder');
        if (btnProcessOrder) {
            btnProcessOrder.addEventListener('click', () => {
                this.navigateToProcess();
            });
        }
    },

    // ==================== INDEX PAGE FUNCTIONS ====================

    // Load order details for Index page
    loadOrderDetails: function (orderId) {
        if (!orderId) {
            OrderDetailsModule.hideOrderDetails();
            return;
        }

        this.currentOrderId = orderId;
        OrderDetailsModule.loadOrderDetails(orderId, '/HRCoordinator/GetOrderDetails');
    },

    // Navigate to process order page
    navigateToProcess: function () {
        if (this.currentOrderId) {
            window.location.href = `/HRCoordinator/ProcessOrder/${this.currentOrderId}`;
        }
    },

    // ==================== PROCESS ORDER PAGE FUNCTIONS ====================

    // Load order details for ProcessOrder page
    loadProcessOrderDetails: function (orderId) {
        $.ajax({
            url: '/HRCoordinator/GetOrderDetails',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ orderId: orderId }),
            headers: {
                'RequestVerificationToken': this.getAntiForgeryToken()
            },
            success: (data) => {
                if (data.success) {
                    if (typeof OrderDetailsModule !== 'undefined') {
                        OrderDetailsModule.populateOrderDetails(data.data);
                    }

                    // Show auto-routing info if available
                    if (data.data.autoRoutingInfo) {
                        this.showAutoPathInfo(data.data.autoRoutingInfo);
                    }

                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Show auto path info panel
    showAutoPathInfo: function (info) {
        const panel = document.getElementById('AutoPathInfoPanel');
        if (panel) {
            panel.innerHTML = `<strong>معلومات التوجيه التلقائي:</strong> ${info}`;
            panel.style.display = 'block';
        }
    },

    // ==================== RESTORE PAGE FUNCTIONS ====================

    // Search for restorable orders
    searchRestorableOrders: function () {
        const searchTerm = document.getElementById('txtRestoreSearch')?.value || '';
        const filter = document.getElementById('ddlRestoreFilter')?.value || 'all';

        const formData = new FormData();
        formData.append('searchTerm', searchTerm);
        formData.append('filter', filter);

        $.ajax({
            url: '/HRCoordinator/GetRestorableOrders',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'RequestVerificationToken': this.getAntiForgeryToken()
            },
            success: (data) => {
                if (data.success) {
                    this.populateRestorableOrders(data.data);
                    OrderDetailsModule.showMessage(data.message, 'success');
                } else {
                    this.populateRestorableOrders([]);
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء البحث عن الطلبات', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Populate restorable orders dropdown
    populateRestorableOrders: function (orders) {
        const select = document.getElementById('ddlRestorableOrders');
        if (select) {
            select.innerHTML = '<option value="">-- اختر الطلب للاستعادة --</option>';

            orders.forEach(order => {
                const option = document.createElement('option');
                option.value = order.value;
                option.textContent = order.text;
                select.appendChild(option);
            });
        }
    },

    // Load restore order details
    loadRestoreOrderDetails: function (orderId) {
        if (!orderId) {
            this.hideRestoreDetails();
            return;
        }

        this.currentRestoreOrderId = orderId;

        $.ajax({
            url: '/HRCoordinator/GetRestoreOrderDetails',
            type: 'POST',
            data: { orderId: orderId },
            headers: {
                'RequestVerificationToken': this.getAntiForgeryToken()
            },
            success: (data) => {
                if (data.success) {
                    this.displayRestoreDetails(data.data);
                    this.showRestoreDetails();
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Display restore details
    displayRestoreDetails: function (data) {
        // Display basic restore info
        const currentStatus = document.getElementById('lblRestoreCurrentStatus');
        const transferDate = document.getElementById('lblRestoreTransferDate');
        const assignedSupervisors = document.getElementById('lblRestoreAssignedSupervisors');

        if (currentStatus) currentStatus.textContent = data.currentStatus || 'غير محدد';
        if (transferDate) transferDate.textContent = data.transferDate || 'غير محدد';
        if (assignedSupervisors) assignedSupervisors.textContent = data.assignedSupervisors || 'غير محدد';

        // Set the order ID for the form
        const restoreOrderId = document.getElementById('restoreOrderId');
        if (restoreOrderId) {
            restoreOrderId.value = this.currentRestoreOrderId;
        }
    },

    // Show restore details section
    showRestoreDetails: function () {
        const detailsPanel = document.getElementById('RestoreDetailsPanel');
        const formSection = document.getElementById('RestoreFormSection');

        if (detailsPanel) detailsPanel.style.display = 'block';
        if (formSection) formSection.style.display = 'block';
    },

    // Hide restore details section
    hideRestoreDetails: function () {
        const detailsPanel = document.getElementById('RestoreDetailsPanel');
        const formSection = document.getElementById('RestoreFormSection');

        if (detailsPanel) detailsPanel.style.display = 'none';
        if (formSection) formSection.style.display = 'none';
        this.currentRestoreOrderId = null;
    },

    // Confirm restore order
    confirmRestore: function () {
        const notes = document.getElementById('restoreNotes')?.value || '';
        if (!this.currentRestoreOrderId) {
            OrderDetailsModule.showMessage('يرجى اختيار طلب للاستعادة', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmRestoreModal;
        if (modal) {
            modal.show();

            // Handle confirmation
            const confirmBtn = document.getElementById('confirmRestoreModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.submitRestore();
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Submit restore order
    submitRestore: function () {
        const formData = new FormData();
        formData.append('orderId', this.currentRestoreOrderId);
        formData.append('restoreNotes', document.getElementById('restoreNotes')?.value || '');
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/RestoreOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    this.clearRestoreForm();
                    // after 1 s refresh the list
                    setTimeout(() => {
                        this.searchRestorableOrders(); // Refresh the list
                    }, 2000);
                    
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء استعادة الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Clear restore form
    clearRestoreForm: function () {
        const notes = document.getElementById('restoreNotes');
        const orderId = document.getElementById('restoreOrderId');

        if (notes) notes.value = '';
        if (orderId) orderId.value = '';
        this.hideRestoreDetails();
    },

    // ==================== SHARED FUNCTIONS ====================

    // Get anti-forgery token
    getAntiForgeryToken: function () {
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        return token ? token.value : '';
    },

    // Show temp data messages
    showTempDataMessages: function () {
        // This will be handled by the inline scripts in the views
        // that call OrderDetailsModule.showMessage()
    },

    // ==================== PROCESSING FUNCTIONS ====================

    // Select all supervisors
    selectAllSupervisors: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    },

    // Unselect all supervisors
    unselectAllSupervisors: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    },

    // Get selected supervisors
    getSelectedSupervisors: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    },

    // Submit order
    submitOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const details = document.getElementById('details')?.value;
        const selectedSupervisors = this.getSelectedSupervisors();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!details || details.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة التفاصيل/الرقم', 'error');
            return;
        }

        if (selectedSupervisors.length === 0) {
            OrderDetailsModule.showMessage('يجب اختيار قسم على الأقل للاعتماد', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmSubmitModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmSubmitModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performSubmitOrder(orderId, details, selectedSupervisors);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform submit order
    performSubmitOrder: function (orderId, details, selectedSupervisors) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('details', details);
        selectedSupervisors.forEach(supervisor => {
            formData.append('selectedSupervisors', supervisor);
        });
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/SubmitOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحويل الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Auto route order
    autoRouteOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmAutoPathModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmAutoPathModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performAutoRoute(orderId);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform auto route
    performAutoRoute: function (orderId) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/AutoRouteOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء التوجيه التلقائي', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Apply predefined path
    applyPath: function (pathNumber) {
        var pathKey = 'مسار' + pathNumber;
        var supervisors = (window.pathsConfiguration && window.pathsConfiguration[pathKey]) || [];
        // Uncheck all
        $('input[name="SelectedSupervisors"]').prop('checked', false);
        // Check only those in the path
        supervisors.forEach(function (sup) {
            $('input[name="SelectedSupervisors"][value="' + sup + '"]').prop('checked', true);
        });
    },

    // Direct to manager
    directToManager: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const details = document.getElementById('details')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!details || details.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة التفاصيل/الرقم', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmDirectToManagerModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmDirectToManagerModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performDirectToManager(orderId, details);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform direct to manager
    performDirectToManager: function (orderId, details) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('details', details);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/DirectToManager',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحويل الطلب للمدير', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Mark order needs action
    markNeedsAction: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const actionRequired = document.getElementById('actionRequired')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!actionRequired || actionRequired.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة الإجراءات المطلوبة', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmActionRequiredModal;
        if (modal) {
            modal.show();
            const confirmBtn = document.getElementById('confirmActionRequiredModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performMarkNeedsAction(orderId, actionRequired);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform mark needs action
    performMarkNeedsAction: function (orderId, actionRequired) {
        if (!orderId || !actionRequired) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب وكتابة الإجراءات المطلوبة', 'error');
            return;
        }
        // Prepare form data
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('actionRequired', actionRequired);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/MarkOrderNeedsAction',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحديد الإجراءات المطلوبة', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Return order
    returnOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const returnReason = document.getElementById('returnReason')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!returnReason || returnReason.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة سبب الإعادة', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmReturnModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmReturnModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performReturnOrder(orderId, returnReason);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform return order
    performReturnOrder: function (orderId, returnReason) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('returnReason', returnReason);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/ReturnOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء إعادة الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Reject order
    rejectOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const rejectReason = document.getElementById('returnReason')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!rejectReason || rejectReason.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة سبب الإلغاء', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmRejectModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmRejectModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performRejectOrder(orderId, rejectReason);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform reject order
    performRejectOrder: function (orderId, rejectReason) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('rejectReason', rejectReason);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/RejectOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء إلغاء الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Download attachments
    downloadAttachments: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        window.location.href = `/HRCoordinator/DownloadAttachments?orderId=${orderId}`;
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    HRCoordinator.init();
});
