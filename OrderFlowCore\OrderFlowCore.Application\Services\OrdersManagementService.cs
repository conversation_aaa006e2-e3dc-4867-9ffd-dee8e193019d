using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Common;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OfficeOpenXml;
using System.IO;

namespace OrderFlowCore.Application.Services
{
    public class OrdersManagementService : IOrdersManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OrdersManagementService> _logger;

        public OrdersManagementService(IUnitOfWork unitOfWork, ILogger<OrdersManagementService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetFilteredOrdersAsync(OrderFilterDto filter)
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                
                // Apply filters
                var filteredOrders = orders.AsQueryable();

                if (filter.Status != "All" && !string.IsNullOrEmpty(filter.Status))
                {
                    filteredOrders = filteredOrders.Where(o => o.OrderStatus == filter.Status);
                }

                if (filter.Department != "All" && !string.IsNullOrEmpty(filter.Department))
                {
                    filteredOrders = filteredOrders.Where(o => o.Department == filter.Department);
                }

                if (filter.FromDate.HasValue)
                {
                    filteredOrders = filteredOrders.Where(o => o.CreatedAt >= filter.FromDate.Value);
                }

                if (filter.ToDate.HasValue)
                {
                    filteredOrders = filteredOrders.Where(o => o.CreatedAt <= filter.ToDate.Value);
                }

                if (!string.IsNullOrEmpty(filter.CivilRecord))
                {
                    filteredOrders = filteredOrders.Where(o => o.CivilRecord.Contains(filter.CivilRecord));
                }

                var result = filteredOrders.Select(o => new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? string.Empty,
                    Department = o.Department ?? string.Empty,
                    OrderStatus = o.OrderStatus.ToDisplayString(),
                    CreatedAt = o.CreatedAt,
                    OrderType = o.OrderType ?? string.Empty,
                    CivilRecord = o.CivilRecord ?? string.Empty
                }).OrderByDescending(o => o.CreatedAt).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الطلبات المفلترة");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAllOrdersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var result = orders.Select(o => new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName,
                    Department = o.Department,
                    OrderStatus = o.OrderStatus,
                    CreatedAt = o.CreatedAt,
                    OrderType = o.OrderType,
                    CivilRecord = o.CivilRecord
                }).OrderByDescending(o => o.CreatedAt).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع الطلبات");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetOrderStatusesAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var statuses = orders.Select(o => o.OrderStatus)
                    .Where(s => !string.IsNullOrEmpty(s))
                    .Distinct()
                    .Select(s => new DropdownItemDto { Value = s, Text = s })
                    .OrderBy(s => s.Text)
                    .ToList();

                statuses.Insert(0, new DropdownItemDto { Value = "All", Text = "كل الحالات" });
                return ServiceResult<List<DropdownItemDto>>.Success(statuses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب حالات الطلبات");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب حالات الطلبات");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetDepartmentsAsync()
        {
            try
            {
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var result = departments.Select(d => new DropdownItemDto
                {
                    Value = d.Name,
                    Text = d.Name
                }).OrderBy(d => d.Text).ToList();

                result.Insert(0, new DropdownItemDto { Value = "All", Text = "كل الأقسام" });
                return ServiceResult<List<DropdownItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأقسام");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب الأقسام");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetOrderNumbersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var result = orders.Select(o => new DropdownItemDto
                {
                    Value = o.Id.ToString(),
                    Text = $"{o.Id} - {o.EmployeeName}"
                }).OrderByDescending(o => int.Parse(o.Value)).ToList();

                return ServiceResult<List<DropdownItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أرقام الطلبات");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب أرقام الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportOrdersToExcelAsync(OrderFilterDto filter)
        {
            try
            {
                var ordersResult = await GetFilteredOrdersAsync(filter);
                if (!ordersResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(ordersResult.Message);
                }

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الطلبات");

                // Headers
                worksheet.Cells[1, 1].Value = "رقم الطلب";
                worksheet.Cells[1, 2].Value = "اسم الموظف";
                worksheet.Cells[1, 3].Value = "القسم";
                worksheet.Cells[1, 4].Value = "حالة الطلب";
                worksheet.Cells[1, 5].Value = "تاريخ الطلب";
                worksheet.Cells[1, 6].Value = "نوع الطلب";
                worksheet.Cells[1, 7].Value = "السجل المدني";

                // Data
                for (int i = 0; i < ordersResult.Data.Count; i++)
                {
                    var order = ordersResult.Data[i];
                    worksheet.Cells[i + 2, 1].Value = order.Id;
                    worksheet.Cells[i + 2, 2].Value = order.EmployeeName;
                    worksheet.Cells[i + 2, 3].Value = order.Department;
                    worksheet.Cells[i + 2, 4].Value = order.OrderStatus;
                    worksheet.Cells[i + 2, 5].Value = order.CreatedAt.ToString("yyyy-MM-dd");
                    worksheet.Cells[i + 2, 6].Value = order.OrderType;
                    worksheet.Cells[i + 2, 7].Value = order.CivilRecord;
                }

                worksheet.Cells.AutoFitColumns();
                return ServiceResult<byte[]>.Success(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الطلبات إلى Excel");
                return ServiceResult<byte[]>.Failure("حدث خطأ أثناء تصدير الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportFilteredResultsToExcelAsync(OrderFilterDto filter)
        {
            return await ExportOrdersToExcelAsync(filter);
        }

        public async Task<ServiceResult<byte[]>> ExportDetailedStatisticsToExcelAsync()
        {
            try
            {
                // This would contain detailed statistics export logic
                // For now, return a simple implementation
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الإحصائيات المفصلة");
                
                worksheet.Cells[1, 1].Value = "تقرير الإحصائيات المفصلة";
                worksheet.Cells[2, 1].Value = "تاريخ التقرير: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                
                return ServiceResult<byte[]>.Success(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الإحصائيات المفصلة");
                return ServiceResult<byte[]>.Failure("حدث خطأ أثناء تصدير الإحصائيات");
            }
        }

        public async Task<ServiceResult> DeleteOldOrdersAsync(DeleteOldOrdersDto deleteRequest, string userName)
        {
            try
            {
                DateTime cutoffDate;
                
                if (deleteRequest.UseCustomDateRange)
                {
                    if (!deleteRequest.StartDate.HasValue || !deleteRequest.EndDate.HasValue)
                    {
                        return ServiceResult.Failure("يجب تحديد تاريخ البداية والنهاية");
                    }
                    
                    var ordersToDelete = await _unitOfWork.Orders.GetOrdersByDateRangeAsync(
                        deleteRequest.StartDate.Value, deleteRequest.EndDate.Value);
                    
                    foreach (var order in ordersToDelete)
                    {
                        await _unitOfWork.Orders.DeleteAsync(order.Id);
                    }
                }
                else
                {
                    cutoffDate = DateTime.Now.AddMonths(-deleteRequest.PeriodInMonths);
                    var ordersToDelete = await _unitOfWork.Orders.GetOrdersOlderThanAsync(cutoffDate);
                    
                    foreach (var order in ordersToDelete)
                    {
                        await _unitOfWork.Orders.DeleteAsync(order.Id);
                    }
                }

                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم حذف الطلبات القديمة بواسطة {UserName}", userName);
                return ServiceResult.Success("تم حذف الطلبات القديمة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلبات القديمة بواسطة {UserName}", userName);
                return ServiceResult.Failure("حدث خطأ أثناء حذف الطلبات القديمة");
            }
        }

        public async Task<ServiceResult> DeleteSpecificOrderAsync(int orderId, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                await _unitOfWork.Orders.DeleteAsync(orderId);
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم حذف الطلب {OrderId} بواسطة {UserName}", orderId, userName);
                return ServiceResult.Success("تم حذف الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلب {OrderId} بواسطة {UserName}", orderId, userName);
                return ServiceResult.Failure("حدث خطأ أثناء حذف الطلب");
            }
        }

        public async Task<ServiceResult> GenerateReportsAsync(string userName)
        {
            try
            {
                // Implementation for report generation
                _logger.LogInformation("تم توليد التقارير بواسطة {UserName}", userName);
                return ServiceResult.Success("تم توليد التقارير بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد التقارير بواسطة {UserName}", userName);
                return ServiceResult.Failure("حدث خطأ أثناء توليد التقارير");
            }
        }
    }
}
