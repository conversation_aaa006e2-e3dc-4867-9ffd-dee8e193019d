@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "متابعة السجلات الخاصة";
}

<div class="container-fluid dashboard-container">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>متابعة السجلات الخاصة</h2>
        <a asp-action="Index" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للقائمة الرئيسية
        </a>
    </div>

    <!-- Message Container for validation and operation feedback -->
    <div id="followUpMessageContainer"></div>

    <!-- Follow-up Records Card -->
    <div class="card dashboard-card mb-4">
        <div class="card-header bg-transparent">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i> إدارة السجلات الخاصة</h5>
            </div>
        </div>
        <div class="card-body">

            <!-- Search and Export/Import Controls -->
            <div class="row mb-4">
                <div class="col-md-6 mb-2 mb-md-0">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" id="followUpSearch" class="form-control" placeholder="بحث في السجلات..." autocomplete="off" />
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-outline-success me-2" id="exportCsvBtn">
                        <i class="fas fa-file-export"></i> تصدير CSV
                    </button>
                    <label class="btn btn-outline-info mb-0">
                        <i class="fas fa-file-import"></i> استيراد CSV
                        <input type="file" id="importCsvInput" accept=".csv" hidden />
                    </label>
                </div>
            </div>

            <!-- Add New Record Card -->
            <div class="card mb-4 border-success">
                <div class="card-header bg-success">
                    <h6 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إضافة سجل جديد</h6>
                </div>
                <div class="card-body">
                    <form id="addFollowUpForm" class="row g-3" autocomplete="off">
                        @Html.AntiForgeryToken()
                        <div class="col-md-3">
                            <label class="form-label fw-bold">السجل المدني</label>
                            <input id="addCivilRegistry" name="CivilRegistry" class="form-control" placeholder="السجل المدني" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">اسم صاحب الطلب</label>
                            <input id="addOwnerName" name="OwnerName" class="form-control" placeholder="اسم صاحب الطلب"  />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">الإجراء المطلوب</label>
                            <input id="addSpecialProcedure" name="SpecialProcedure" class="form-control" placeholder="الإجراء المطلوب" />
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-plus"></i> إضافة للمتابعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Records Table Card -->
            <div class="card">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-table me-2"></i>جدول السجلات</h6>
                </div>
                <div class="card-body p-0">
                    <table class="table table-hover mb-0" id="followUpTable">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-id-card me-2"></i>السجل المدني</th>
                                <th><i class="fas fa-user me-2"></i>اسم صاحب الطلب</th>
                                <th><i class="fas fa-tasks me-2"></i>الإجراء المطلوب</th>
                                <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var rec in Model.FollowUpRecords)
                            {
                                <tr class="followup-row" data-civil="@rec.CivilRecord" data-owner="@rec.OwnerName" data-proc="@rec.SpecialProcedure">
                                    <td class="fw-bold">@rec.CivilRecord</td>
                                    <td>@rec.OwnerName</td>
                                    <td>@rec.SpecialProcedure</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary edit-btn me-1">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-btn">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination Controls -->
            <div id="followUpPagination" class="d-flex justify-content-center mt-3"></div>

        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">تعديل السجل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editFollowUpForm" autocomplete="off">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="editCivilRegistry" name="CivilRecord" />
                    <div class="mb-3">
                        <label class="form-label">اسم صاحب الطلب</label>
                        <input type="text" id="editOwnerName" name="OwnerName" class="form-control" />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الإجراء المطلوب</label>
                        <input type="text" id="editSpecialProcedure" name="SpecialProcedure" class="form-control" />
                    </div>
                    <button type="submit" class="btn btn-success">حفظ التعديل</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد أنك تريد حذف هذا السجل؟</p>
                <div id="deleteModalRecordInfo" class="mb-2"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/supervisorOrders.js"></script>
}
