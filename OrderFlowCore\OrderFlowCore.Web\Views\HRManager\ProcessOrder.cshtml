@model OrderFlowCore.Web.ViewModels.HRManagerProcessOrderViewModel
@{
    ViewData["Title"] = "معالجة الطلبات - مدير الموارد البشرية";
}

<div class="container mt-4">
    @Html.AntiForgeryToken()
    
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>معالجة الطلبات - مدير الموارد البشرية</h2>
                <a href="@Url.Action("Index", "HRManager")" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة الرئيسية
                </a>
            </div>

            <!-- Order Selection Section -->
            <div class="order-select-container mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="orderSelect" class="form-label fw-bold">📋 رقم الطلب:</label>
                        @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "-- اختر رقم الطلب --",
                            new { @class = "form-select", @id = "orderSelect"})
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <p class="mb-0">اختر طلباً لعرض تفاصيله واتخاذ الإجراء المناسب</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Processing Section -->
            <div class="row">
                <!-- Right Column: Actions -->
                <div class="col-lg-4">
                    <!-- Primary Actions -->
                    <div class="card mb-3 border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-play-circle"></i> الإجراءات الرئيسية</h6>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-success btn-lg w-100 mb-3" id="btnApprove">
                                <i class="fas fa-check-circle"></i> اعتماد الطلب
                            </button>
                            <button type="button" class="btn btn-danger btn-lg w-100 mb-3" id="btnReject">
                                <i class="fas fa-times-circle"></i> إلغاء الطلب
                            </button>
                            <button type="button" class="btn btn-warning btn-lg w-100 mb-3" id="btnReturn">
                                <i class="fas fa-undo"></i> إعادة الطلب
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mb-3">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-outline-secondary w-100" id="btnDownload">
                                <i class="fas fa-download"></i> تحميل مرفقات الطلب
                            </button>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="card mb-3">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-cog"></i> الإعدادات</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                @Html.CheckBoxFor(m => m.AutoDeleteAttachments, new { @class = "form-check-input", @id = "chkAutoDelete" })
                                <label class="form-check-label" for="chkAutoDelete">
                                    تفعيل حذف المرفقات تلقائياً عند اعتماد الطلب
                                </label>
                            </div>
                            <div class="mt-2">
                                <small id="autoDeleteStatus" class="text-muted"></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Left Column: Form Fields -->
                <div class="col-lg-8">
                    <!-- Form Details Card -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-edit"></i> تفاصيل المعالجة</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="rejectReason" class="form-label fw-bold">سبب الإلغاء/الإعادة</label>
                                <textarea class="form-control" id="rejectReason" name="rejectReason" 
                                    placeholder="اذكر سبب الإلغاء أو الإعادة..." rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Message Container -->
                    <div id="messageContainer" class="mt-3"></div>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="loading text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري المعالجة...</p>
            </div>

            <!-- Order Details Summary -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-file-alt"></i> ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    @await Html.PartialAsync("_OrderDetailsPartial")
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Approve Modal -->
<div class="modal fade" id="confirmApproveModal" tabindex="-1" aria-labelledby="confirmApproveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmApproveModalLabel">تأكيد اعتماد الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من اعتماد هذا الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmApproveModalBtn">نعم، اعتماد</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Reject Modal -->
<div class="modal fade" id="confirmRejectModal" tabindex="-1" aria-labelledby="confirmRejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmRejectModalLabel">تأكيد إلغاء الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إلغاء هذا الطلب؟
                <div class="mt-3">
                    <label for="rejectReasonModal" class="form-label">سبب الإلغاء:</label>
                    <textarea class="form-control" id="rejectReasonModal" readonly rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRejectModalBtn">نعم، إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Return Modal -->
<div class="modal fade" id="confirmReturnModal" tabindex="-1" aria-labelledby="confirmReturnModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmReturnModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إعادة هذا الطلب؟
                <div class="mt-3">
                    <label for="returnReasonModal" class="form-label">سبب الإعادة:</label>
                    <textarea class="form-control" id="returnReasonModal" readonly rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="confirmReturnModalBtn">نعم، إعادة</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/hrmanager.js"></script>
} 