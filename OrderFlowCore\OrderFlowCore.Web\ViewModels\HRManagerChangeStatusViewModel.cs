using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class HRManagerChangeStatusViewModel
    {
        public HRManagerChangeStatusViewModel()
        {
            StatusChangeOrderNumbers = new List<SelectListItem>();
            AvailableStatuses = new List<SelectListItem>();
        }

        // Status change section
        public List<SelectListItem> StatusChangeOrderNumbers { get; set; }
        public int SelectedStatusChangeOrderId { get; set; }
        public List<SelectListItem> AvailableStatuses { get; set; }
        public string SelectedNewStatus { get; set; } = string.Empty;

        [Display(Name = "ملاحظات التغيير")]
        public string StatusChangeNotes { get; set; } = string.Empty;

        // Current order details for status change
        public string CurrentStatus { get; set; } = string.Empty;
        public string OrderType { get; set; } = string.Empty;

        // UI State
        public bool ShowStatusChangeDetails { get; set; }

        // Search and filter for status change
        public string SearchTerm { get; set; } = string.Empty;
        public string FilterPeriod { get; set; } = "today";
    }
} 