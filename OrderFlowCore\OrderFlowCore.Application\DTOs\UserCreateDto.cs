using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.DTOs
{
    public class UserCreateDto
    {
        public string Username { get; set; } = string.Empty;
        [DataType(DataType.EmailAddress)]
        public string? Email { get; set; }
        [DataType(DataType.PhoneNumber)]
        public string? Phone { get; set; }
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;

        // New role system properties
        public UserRole UserRole { get; set; } 
        public string? RoleType { get; set; }
    }
}