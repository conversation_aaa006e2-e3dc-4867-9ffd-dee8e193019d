using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrderManagementService
{
    Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync();
    Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto);
    Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId);
    Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsByIdAndCivilRecordAsync(int orderId, string civilRecord);
    Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, string fileName, int fileNumber);
    Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId);
}
