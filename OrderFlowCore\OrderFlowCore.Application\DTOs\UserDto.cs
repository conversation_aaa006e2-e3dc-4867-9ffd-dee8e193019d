using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.DTOs;

public class UserDto
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }

    public UserRole UserRole { get; set; } = UserRole.DirectManager;
    public string? RoleType { get; set; }
}