using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class PathManagementController : Controller
    {
        private readonly IPathManagementService _pathManagementService;
        private readonly ILogger<PathManagementController> _logger;

        public PathManagementController(
            IPathManagementService pathManagementService,
            ILogger<PathManagementController> logger)
        {
            _pathManagementService = pathManagementService;
            _logger = logger;
        }

        [HttpGet]
        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveManualPaths([FromBody] Dictionary<string, List<string>> paths)
        {
            try
            {
                var result = await _pathManagementService.UpdateAllManualPathsAsync(paths);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = result.Message });
                }
                return Json(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ جميع تغييرات المسارات اليدوية");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ التغييرات" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveAutoRoute(AutoRouteFormViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = "يرجى التحقق من البيانات المدخلة" });
                }

                if (model.SelectedSupervisors == null || !model.SelectedSupervisors.Any())
                {
                    return Json(new { success = false, message = "يرجى تحديد مشرف واحد على الأقل" });
                }

                var userName = User.Identity?.Name ?? "System";

                if (model.IsEditing)
                {
                    var updateDto = new UpdateAutoRouteDto
                    {
                        OrderType = model.OrderType,
                        Nationality = model.Nationality,
                        Job = model.Job,
                        Supervisors = model.SelectedSupervisors,
                        Status = model.Status,
                    };

                    var result = await _pathManagementService.UpdateAutoRouteAsync(model.Id.Value, updateDto, userName);
                    if (result.IsSuccess)
                    {
                        return Json(new { success = true, message = result.Message });
                    }
                    else
                    {
                        return Json(new { success = false, message = result.Message });
                    }
                }
                else
                {
                    var createDto = new CreateAutoRouteDto
                    {
                        OrderType = model.OrderType,
                        Nationality = model.Nationality,
                        Job = model.Job,
                        Supervisors = model.SelectedSupervisors,
                        Status = model.Status,
                    };

                    var result = await _pathManagementService.CreateAutoRouteAsync(createDto, userName);
                    if (result.IsSuccess)
                    {
                        return Json(new { success = true, message = result.Message });
                    }
                    else
                    {
                        return Json(new { success = false, message = result.Message });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ المسار التلقائي");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ المسار التلقائي" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveDirectRoute(DirectRouteFormViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = "يرجى التحقق من البيانات المدخلة" });
                }

                if (model.SelectedSupervisors == null || !model.SelectedSupervisors.Any())
                {
                    return Json(new { success = false, message = "يرجى تحديد مشرف واحد على الأقل" });
                }

                var userName = User.Identity?.Name ?? "System";

                if (model.IsEditing)
                {
                    var updateDto = new UpdateDirectRouteDto
                    {
                        OrderType = model.OrderType,
                        Nationality = model.Nationality,
                        Job = model.Job,
                        Supervisors = model.SelectedSupervisors,
                        Status = model.Status,
                    };

                    var result = await _pathManagementService.UpdateDirectRouteAsync(model.Id.Value, updateDto, userName);
                    if (result.IsSuccess)
                    {
                        return Json(new { success = true, message = result.Message });
                    }
                    else
                    {
                        return Json(new { success = false, message = result.Message });
                    }
                }
                else
                {
                    var createDto = new CreateDirectRouteDto
                    {
                        OrderType = model.OrderType,
                        Nationality = model.Nationality,
                        Job = model.Job,
                        Supervisors = model.SelectedSupervisors,
                        Status = model.Status,
                    };

                    var result = await _pathManagementService.CreateDirectRouteAsync(createDto, userName);
                    if (result.IsSuccess)
                    {
                        return Json(new { success = true, message = result.Message });
                    }
                    else
                    {
                        return Json(new { success = false, message = result.Message });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ المسار التلقائي");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ المسار التلقائي" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAutoRoute(int id)
        {
            try
            {
                var result = await _pathManagementService.DeleteAutoRouteAsync(id);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = result.Message });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المسار التلقائي {Id}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف المسار" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteDirectRoute(int id)
        {
            try
            {
                var result = await _pathManagementService.DeleteDirectRouteAsync(id);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, message = result.Message });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المسار السريع {Id}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف المسار" });
            }
        }

        #region Manual Routing
        [HttpGet]
        public async Task<IActionResult> ManualTabPartial()
        {
            var model = await BuildDashboardViewModelAsync();
            return PartialView("_ManualPathPartial", model);
        }
        #endregion

        #region Auto Routing
        [HttpGet]
        public async Task<IActionResult> AutoTabPartial()
        {
            var model = await BuildDashboardViewModelAsync();
            return PartialView("_AutoPathPartial", model);
        }
        #endregion

        [HttpGet]
        public async Task<IActionResult> GetAutoRoute(int id)
        {
            var result = await _pathManagementService.GetAutoRouteByIdAsync(id);
            if (result.IsSuccess)
            {
                return Json(new { success = true, data = result.Data });
            }
            return Json(new { success = false, message = result.Message });
        }

        [HttpGet]
        public async Task<IActionResult> GetDirectRoute(int id)
        {
            var result = await _pathManagementService.GetDirectRouteByIdAsync(id);
            if (result.IsSuccess)
            {
                return Json(new { success = true, data = result.Data });
            }
            return Json(new { success = false, message = result.Message });
        }

        #region Direct Routing
        [HttpGet]
        public async Task<IActionResult> DirectTabPartial()
        {
            var model = await BuildDashboardViewModelAsync();
            return PartialView("_DirectPathPartial", model);
        }
        #endregion

        // Helper method to build the dashboard view model (refactored from Index)
        private async Task<PathManagementDashboardViewModel> BuildDashboardViewModelAsync()
        {
            var model = new PathManagementDashboardViewModel();
            // Manual Routing
            var supervisorsResult = await _pathManagementService.GetAvailableSupervisorsAsync();
            if (supervisorsResult.IsSuccess)
            {
                model.AvailableSupervisors = supervisorsResult.Data;
                model.TotalAvailableSupervisors = supervisorsResult.Data.Count;
            }
            var pathsConfigResult = await _pathManagementService.GetAllPathsConfigurationAsync();
            if (pathsConfigResult.IsSuccess)
            {
                model.PathsConfiguration = pathsConfigResult.Data;
                model.ActiveManualPathsCount = model.PathsConfiguration.Count;
            }
            // Auto Routing
            var autoRoutesResult = await _pathManagementService.GetAutoRoutesAsync();
            if (autoRoutesResult.IsSuccess)
            {
                model.AutoRoutes = autoRoutesResult.Data;
            }
            var autoCountResult = await _pathManagementService.GetActiveAutoRoutesCountAsync();
            if (autoCountResult.IsSuccess)
            {
                model.ActiveAutoRoutesCount = autoCountResult.Data;
            }
            var dropdownResult = await _pathManagementService.GetDropdownDataAsync();
            if (dropdownResult.IsSuccess)
            {
                model.AutoOrderTypes = dropdownResult.Data.OrderTypes.Select(ot => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem { Value = ot, Text = ot }).ToList();
                model.AutoJobTypes = dropdownResult.Data.JobTypes.Select(jt => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem { Value = jt, Text = jt }).ToList();
                model.AutoNationalities = dropdownResult.Data.Nationalities.Select(n => new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem { Value = n, Text = n }).ToList();
                model.AutoSupervisors = model.AvailableSupervisors;
            }
            // Direct Routing
            var directRoutesResult = await _pathManagementService.GetDirectRoutesAsync();
            if (directRoutesResult.IsSuccess)
            {
                model.DirectRoutes = directRoutesResult.Data;
            }
            var directCountResult = await _pathManagementService.GetActiveDirectRoutesCountAsync();
            if (directCountResult.IsSuccess)
            {
                model.ActiveDirectRoutesCount = directCountResult.Data;
            }
            model.DirectOrderTypes = model.AutoOrderTypes;
            model.DirectJobTypes = model.AutoJobTypes;
            model.DirectNationalities = model.AutoNationalities;
            model.DirectSupervisors = model.AvailableSupervisors;
            return model;
        }
    }
}
