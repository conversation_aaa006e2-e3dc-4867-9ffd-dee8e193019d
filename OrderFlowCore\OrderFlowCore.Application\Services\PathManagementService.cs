using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace OrderFlowCore.Application.Services
{
    public class PathManagementService : IPathManagementService
    {
        private readonly IPathsTableRepository _pathsTableRepository;
        private readonly IAutoRoutingRepository _autoRoutingRepository;
        private readonly IDirectRoutingRepository _directRoutingRepository;
        private readonly IOrdersTypeRepository _ordersTypeRepository;
        private readonly IJobTypeRepository _jobTypeRepository;
        private readonly INationalityRepository _nationalityRepository;
        private readonly ILogger<PathManagementService> _logger;

        // Available supervisors list
        private readonly List<string> _availableSupervisors = new List<string>
        {
            "خدمات الموظفين",
            "إدارة تخطيط الموارد البشرية",
            "إدارة تقنية المعلومات",
            "مراقبة الدوام",
            "السجلات الطبية",
            "إدارة الرواتب والاستحقاقات",
            "إدارة القانونية والالتزام",
            "خدمات الموارد البشرية",
            "إدارة الإسكان",
            "قسم الملفات",
            "العيادات الخارجية",
            "التأمينات الاجتماعية",
            "وحدة مراقبة المخزون",
            "إدارة تنمية الإيرادات",
            "إدارة الأمن و السلامة",
            "الطب الاتصالي"
        };

        public PathManagementService(
            IPathsTableRepository pathsTableRepository,
            IAutoRoutingRepository autoRoutingRepository,
            IDirectRoutingRepository directRoutingRepository,
            IOrdersTypeRepository ordersTypeRepository,
            IJobTypeRepository jobTypeRepository,
            INationalityRepository nationalityRepository,
            ILogger<PathManagementService> logger)
        {
            _pathsTableRepository = pathsTableRepository;
            _autoRoutingRepository = autoRoutingRepository;
            _directRoutingRepository = directRoutingRepository;
            _ordersTypeRepository = ordersTypeRepository;
            _jobTypeRepository = jobTypeRepository;
            _nationalityRepository = nationalityRepository;
            _logger = logger;
        }

        #region Manual Routing (PathsTable) Operations

        public async Task<ServiceResult<List<string>>> GetPathSupervisorsAsync(string pathColumn)
        {
            try
            {
                var supervisors = await _pathsTableRepository.GetPathSupervisorsAsync(pathColumn);
                return ServiceResult<List<string>>.Success(supervisors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مشرفي المسار {PathColumn}", pathColumn);
                return ServiceResult<List<string>>.Failure($"خطأ في جلب مشرفي المسار: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UpdatePathSupervisorAsync(string pathColumn, string supervisorText, bool isChecked)
        {
            try
            {
                if (isChecked)
                {
                    await _pathsTableRepository.AddToPathAsync(supervisorText, pathColumn);
                }
                else
                {
                    await _pathsTableRepository.RemoveFromPathAsync(supervisorText, pathColumn);
                }

                return ServiceResult.Success("تم تحديث المسار بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث مشرف المسار {PathColumn}", pathColumn);
                return ServiceResult.Failure($"خطأ في تحديث مشرف المسار: {ex.Message}");
            }
        }

        public async Task<ServiceResult<Dictionary<string, List<string>>>> GetAllPathsConfigurationAsync()
        {
            try
            {
                var pathsConfiguration = await _pathsTableRepository.GetAllPathsConfigurationAsync();
                return ServiceResult<Dictionary<string, List<string>>>.Success(pathsConfiguration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات المسارات");
                return ServiceResult<Dictionary<string, List<string>>>.Failure($"خطأ في جلب إعدادات المسارات: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UpdateAllManualPathsAsync(Dictionary<string, List<string>> paths)
        {
            try
            {
                // For each path, clear all supervisors and add the new ones
                foreach (var kvp in paths)
                {
                    var pathColumn = kvp.Key;
                    var newSupervisors = kvp.Value ?? new List<string>();

                    // Get current supervisors
                    var currentSupervisors = await _pathsTableRepository.GetPathSupervisorsAsync(pathColumn) ?? new List<string>();

                    // Remove all current supervisors not in the new list
                    foreach (var supervisor in currentSupervisors)
                    {
                        if (!newSupervisors.Contains(supervisor))
                        {
                            await _pathsTableRepository.RemoveFromPathAsync(supervisor, pathColumn);
                        }
                    }

                    // Add new supervisors not already present
                    foreach (var supervisor in newSupervisors)
                    {
                        if (!currentSupervisors.Contains(supervisor))
                        {
                            await _pathsTableRepository.AddToPathAsync(supervisor, pathColumn);
                        }
                    }
                }
                return ServiceResult.Success("تم حفظ جميع التغييرات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ جميع تغييرات المسارات اليدوية");
                return ServiceResult.Failure("حدث خطأ أثناء حفظ التغييرات");
            }
        }

        #endregion

        #region Auto Routing Operations

        public async Task<ServiceResult<List<AutoRouteDto>>> GetAutoRoutesAsync()
        {
            try
            {
                var autoRoutes = await _autoRoutingRepository.GetAllAsync();
                var autoRouteDtos = autoRoutes.Select(MapToAutoRouteDto).ToList();
                return ServiceResult<List<AutoRouteDto>>.Success(autoRouteDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المسارات التلقائية");
                return ServiceResult<List<AutoRouteDto>>.Failure($"خطأ في جلب المسارات التلقائية: {ex.Message}");
            }
        }

        public async Task<ServiceResult<AutoRouteDto>> GetAutoRouteByIdAsync(int id)
        {
            try
            {
                var autoRoute = await _autoRoutingRepository.GetByIdAsync(id);
                if (autoRoute == null)
                {
                    return ServiceResult<AutoRouteDto>.Failure("المسار التلقائي غير موجود");
                }

                var autoRouteDto = MapToAutoRouteDto(autoRoute);
                return ServiceResult<AutoRouteDto>.Success(autoRouteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المسار التلقائي {Id}", id);
                return ServiceResult<AutoRouteDto>.Failure($"خطأ في جلب المسار التلقائي: {ex.Message}");
            }
        }

        public async Task<ServiceResult<AutoRouteDto>> CreateAutoRouteAsync(CreateAutoRouteDto dto, string userName)
        {
            try
            {
                // Check for duplicates
                var duplicateExists = await _autoRoutingRepository.ExistsAsync(dto.OrderType, dto.Nationality, dto.Job);
                if (duplicateExists)
                {
                    return ServiceResult<AutoRouteDto>.Failure("يوجد مسار مسجل مسبقاً لنفس الإعدادات");
                }

                var autoRoute = new AutoRouting
                {
                    OrderType = dto.OrderType,
                    Nationality = dto.Nationality,
                    Job = dto.Job,
                    Supervisors = string.Join(";", dto.Supervisors),
                    Status = dto.Status,
                    Notes = dto.Notes,
                    CreatedBy = userName
                };

                var createdAutoRoute = await _autoRoutingRepository.AddAsync(autoRoute);
                var autoRouteDto = MapToAutoRouteDto(createdAutoRoute);

                return ServiceResult<AutoRouteDto>.Success(autoRouteDto, "تم إضافة المسار التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المسار التلقائي");
                return ServiceResult<AutoRouteDto>.Failure($"خطأ في إنشاء المسار التلقائي: {ex.Message}");
            }
        }

        public async Task<ServiceResult<AutoRouteDto>> UpdateAutoRouteAsync(int id, UpdateAutoRouteDto dto, string userName)
        {
            try
            {
                var autoRoute = await _autoRoutingRepository.GetByIdAsync(id);
                if (autoRoute == null)
                {
                    return ServiceResult<AutoRouteDto>.Failure("المسار التلقائي غير موجود");
                }

                // Check for duplicates excluding current route
                var duplicateExists = await _autoRoutingRepository.ExistsAsync(dto.OrderType, dto.Nationality, dto.Job, id);
                if (duplicateExists)
                {
                    return ServiceResult<AutoRouteDto>.Failure("يوجد مسار مسجل مسبقاً لنفس الإعدادات");
                }

                autoRoute.OrderType = dto.OrderType;
                autoRoute.Nationality = dto.Nationality;
                autoRoute.Job = dto.Job;
                autoRoute.Supervisors = string.Join(";", dto.Supervisors);
                autoRoute.Status = dto.Status;
                autoRoute.Notes = dto.Notes;
                autoRoute.ModifiedBy = userName;

                await _autoRoutingRepository.UpdateAsync(autoRoute);
                var autoRouteDto = MapToAutoRouteDto(autoRoute);

                return ServiceResult<AutoRouteDto>.Success(autoRouteDto, "تم تحديث المسار التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المسار التلقائي {Id}", id);
                return ServiceResult<AutoRouteDto>.Failure($"خطأ في تحديث المسار التلقائي: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ToggleAutoRouteStatusAsync(int id)
        {
            try
            {
                var autoRoute = await _autoRoutingRepository.GetByIdAsync(id);
                if (autoRoute == null)
                {
                    return ServiceResult.Failure("المسار التلقائي غير موجود");
                }

                autoRoute.Status = !autoRoute.Status;
                await _autoRoutingRepository.UpdateAsync(autoRoute);

                var statusText = autoRoute.Status ? "تم تفعيل" : "تم إلغاء تفعيل";
                return ServiceResult.Success($"{statusText} المسار التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير حالة المسار التلقائي {Id}", id);
                return ServiceResult.Failure($"خطأ في تغيير حالة المسار التلقائي: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteAutoRouteAsync(int id)
        {
            try
            {
                await _autoRoutingRepository.DeleteAsync(id);
                return ServiceResult.Success("تم حذف المسار التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المسار التلقائي {Id}", id);
                return ServiceResult.Failure($"خطأ في حذف المسار التلقائي: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> CheckAutoRouteDuplicateAsync(string requestType, string nationality, string job, int? excludeId = null)
        {
            try
            {
                var exists = await _autoRoutingRepository.ExistsAsync(requestType, nationality, job, excludeId);
                return ServiceResult<bool>.Success(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من تكرار المسار التلقائي");
                return ServiceResult<bool>.Failure($"خطأ في التحقق من تكرار المسار التلقائي: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> GetActiveAutoRoutesCountAsync()
        {
            try
            {
                var count = await _autoRoutingRepository.GetActiveCountAsync();
                return ServiceResult<int>.Success(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عدد المسارات التلقائية النشطة");
                return ServiceResult<int>.Failure($"خطأ في جلب عدد المسارات التلقائية النشطة: {ex.Message}");
            }
        }

        #endregion

        #region Direct Routing Operations

        public async Task<ServiceResult<List<DirectRouteDto>>> GetDirectRoutesAsync()
        {
            try
            {
                var directRoutes = await _directRoutingRepository.GetAllAsync();
                var directRouteDtos = directRoutes.Select(MapToDirectRouteDto).ToList();
                return ServiceResult<List<DirectRouteDto>>.Success(directRouteDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المسارات السريعة");
                return ServiceResult<List<DirectRouteDto>>.Failure($"خطأ في جلب المسارات السريعة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<DirectRouteDto>> GetDirectRouteByIdAsync(int id)
        {
            try
            {
                var directRoute = await _directRoutingRepository.GetByIdAsync(id);
                if (directRoute == null)
                {
                    return ServiceResult<DirectRouteDto>.Failure("المسار السريع غير موجود");
                }

                var directRouteDto = MapToDirectRouteDto(directRoute);
                return ServiceResult<DirectRouteDto>.Success(directRouteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المسار السريع {Id}", id);
                return ServiceResult<DirectRouteDto>.Failure($"خطأ في جلب المسار السريع: {ex.Message}");
            }
        }

        public async Task<ServiceResult<DirectRouteDto>> CreateDirectRouteAsync(CreateDirectRouteDto dto, string userName)
        {
            try
            {
                // Check for duplicates
                var duplicateExists = await _directRoutingRepository.ExistsAsync(dto.OrderType, dto.Nationality, dto.Job);
                if (duplicateExists)
                {
                    return ServiceResult<DirectRouteDto>.Failure("يوجد مسار مسجل مسبقاً لنفس الإعدادات");
                }

                var directRoute = new DirectRouting
                {
                    OrderType = dto.OrderType,
                    Nationality = dto.Nationality,
                    Job = dto.Job,
                    Supervisors = string.Join(";", dto.Supervisors),
                    Status = dto.Status,
                    Notes = dto.Notes,
                    CreatedBy = userName
                };

                var createdDirectRoute = await _directRoutingRepository.AddAsync(directRoute);
                var directRouteDto = MapToDirectRouteDto(createdDirectRoute);

                return ServiceResult<DirectRouteDto>.Success(directRouteDto, "تم إضافة المسار السريع بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المسار السريع");
                return ServiceResult<DirectRouteDto>.Failure($"خطأ في إنشاء المسار السريع: {ex.Message}");
            }
        }

        public async Task<ServiceResult<DirectRouteDto>> UpdateDirectRouteAsync(int id, UpdateDirectRouteDto dto, string userName)
        {
            try
            {
                var directRoute = await _directRoutingRepository.GetByIdAsync(id);
                if (directRoute == null)
                {
                    return ServiceResult<DirectRouteDto>.Failure("المسار السريع غير موجود");
                }

                // Check for duplicates excluding current route
                var duplicateExists = await _directRoutingRepository.ExistsAsync(dto.OrderType, dto.Nationality, dto.Job, id);
                if (duplicateExists)
                {
                    return ServiceResult<DirectRouteDto>.Failure("يوجد مسار مسجل مسبقاً لنفس الإعدادات");
                }

                directRoute.OrderType = dto.OrderType;
                directRoute.Nationality = dto.Nationality;
                directRoute.Job = dto.Job;
                directRoute.Supervisors = string.Join(";", dto.Supervisors);
                directRoute.Status = dto.Status;
                directRoute.Notes = dto.Notes;
                directRoute.ModifiedBy = userName;

                await _directRoutingRepository.UpdateAsync(directRoute);
                var directRouteDto = MapToDirectRouteDto(directRoute);

                return ServiceResult<DirectRouteDto>.Success(directRouteDto, "تم تحديث المسار السريع بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المسار السريع {Id}", id);
                return ServiceResult<DirectRouteDto>.Failure($"خطأ في تحديث المسار السريع: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ToggleDirectRouteStatusAsync(int id)
        {
            try
            {
                var directRoute = await _directRoutingRepository.GetByIdAsync(id);
                if (directRoute == null)
                {
                    return ServiceResult.Failure("المسار السريع غير موجود");
                }

                directRoute.Status = !directRoute.Status;
                await _directRoutingRepository.UpdateAsync(directRoute);

                var statusText = directRoute.Status ? "تم تفعيل" : "تم إلغاء تفعيل";
                return ServiceResult.Success($"{statusText} المسار السريع بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير حالة المسار السريع {Id}", id);
                return ServiceResult.Failure($"خطأ في تغيير حالة المسار السريع: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteDirectRouteAsync(int id)
        {
            try
            {
                await _directRoutingRepository.DeleteAsync(id);
                return ServiceResult.Success("تم حذف المسار السريع بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المسار السريع {Id}", id);
                return ServiceResult.Failure($"خطأ في حذف المسار السريع: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> CheckDirectRouteDuplicateAsync(string requestType, string nationality, string job, int? excludeId = null)
        {
            try
            {
                var exists = await _directRoutingRepository.ExistsAsync(requestType, nationality, job, excludeId);
                return ServiceResult<bool>.Success(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من تكرار المسار السريع");
                return ServiceResult<bool>.Failure($"خطأ في التحقق من تكرار المسار السريع: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> GetActiveDirectRoutesCountAsync()
        {
            try
            {
                var count = await _directRoutingRepository.GetActiveCountAsync();
                return ServiceResult<int>.Success(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عدد المسارات السريعة النشطة");
                return ServiceResult<int>.Failure($"خطأ في جلب عدد المسارات السريعة النشطة: {ex.Message}");
            }
        }

        #endregion

        #region Dropdown Data and Other Operations

        public async Task<ServiceResult<PathManagementDropdownDataDto>> GetDropdownDataAsync()
        {
            try
            {
                var dropdownData = new PathManagementDropdownDataDto();

                // Get order types
                var orderTypes = await _ordersTypeRepository.GetAllAsync();
                dropdownData.OrderTypes = orderTypes.Select(ot => ot.Name).ToList();

                // Get job types
                var jobTypes = await _jobTypeRepository.GetAllAsync();
                dropdownData.JobTypes = jobTypes.Select(jt => jt.Name).ToList();

                // Add special options
                dropdownData.JobTypes.Insert(0, "الكل");

                // Get nationalities
                var nationalities = await _nationalityRepository.GetAllAsync();
                dropdownData.Nationalities = nationalities.Select(n => n.Name).ToList();

                // Set supervisors
                dropdownData.Supervisors = _availableSupervisors;

                return ServiceResult<PathManagementDropdownDataDto>.Success(dropdownData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب بيانات القوائم المنسدلة");
                return ServiceResult<PathManagementDropdownDataDto>.Failure($"خطأ في جلب بيانات القوائم المنسدلة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<string>>> GetAvailableSupervisorsAsync()
        {
            try
            {
                return ServiceResult<List<string>>.Success(_availableSupervisors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب قائمة المشرفين");
                return ServiceResult<List<string>>.Failure($"خطأ في جلب قائمة المشرفين: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UpdateEmployeeCountAsync()
        {
            try
            {
                // This method was in the original code but seems to be a placeholder
                // Implementation would depend on specific business requirements
                await Task.CompletedTask;
                return ServiceResult.Success("تم تحديث عدد الموظفين بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث عدد الموظفين");
                return ServiceResult.Failure($"خطأ في تحديث عدد الموظفين: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private AutoRouteDto MapToAutoRouteDto(AutoRouting autoRoute)
        {
            return new AutoRouteDto
            {
                Id = autoRoute.Id,
                OrderType = autoRoute.OrderType,
                Nationality = autoRoute.Nationality,
                Job = autoRoute.Job,
                Supervisors = string.IsNullOrEmpty(autoRoute.Supervisors)
                    ? new List<string>()
                    : autoRoute.Supervisors.Split(';').ToList(),
                Status = autoRoute.Status,
                Notes = autoRoute.Notes,
                CreatedAt = autoRoute.CreatedAt,
                CreatedBy = autoRoute.CreatedBy,
                ModifiedAt = autoRoute.ModifiedAt,
                ModifiedBy = autoRoute.ModifiedBy
            };
        }

        private DirectRouteDto MapToDirectRouteDto(DirectRouting directRoute)
        {
            return new DirectRouteDto
            {
                Id = directRoute.Id,
                OrderType = directRoute.OrderType,
                Nationality = directRoute.Nationality,
                Job = directRoute.Job,
                Supervisors = string.IsNullOrEmpty(directRoute.Supervisors)
                    ? new List<string>()
                    : directRoute.Supervisors.Split(';').ToList(),
                Status = directRoute.Status,
                Notes = directRoute.Notes,
                CreatedAt = directRoute.CreatedAt,
                CreatedBy = directRoute.CreatedBy,
                ModifiedAt = directRoute.ModifiedAt,
                ModifiedBy = directRoute.ModifiedBy
            };
        }

        #endregion
    }
}
