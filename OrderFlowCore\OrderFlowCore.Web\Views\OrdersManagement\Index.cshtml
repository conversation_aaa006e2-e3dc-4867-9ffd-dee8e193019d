@model OrderFlowCore.Web.ViewModels.OrdersManagementViewModel
@{
    ViewData["Title"] = "إدارة الطلبات";
}

<div class="container-fluid mt-4">
    <!-- Page Header -->
    <div class="welcome-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-clipboard-list ml-2"></i>إدارة الطلبات</h2>
                <p>عرض وإدارة جميع الطلبات مع إمكانيات التصفية والتصدير</p>
            </div>
            <div class="col-md-4 text-left">
                <div class="stat-card primary">
                    <div class="stat-number">@Model.TotalOrdersCount</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle ml-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle ml-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle ml-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Filter Section -->
    <div class="card-custom mb-4">
        <div class="card-header-custom">
            <h5 class="mb-0"><i class="fas fa-filter ml-2"></i>خيارات التصفية والبحث</h5>
        </div>
        <div class="card-body">
            @using (Html.BeginForm("FilterOrders", "OrdersManagement", FormMethod.Post, new { @class = "needs-validation", novalidate = "novalidate" }))
            {
                @Html.AntiForgeryToken()
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="SelectedStatus" class="form-label">حالة الطلب</label>
                        @Html.DropDownListFor(m => m.SelectedStatus, Model.StatusOptions, new { @class = "form-select" })
                    </div>
                    <div class="col-md-3">
                        <label for="SelectedDepartment" class="form-label">القسم</label>
                        @Html.DropDownListFor(m => m.SelectedDepartment, Model.DepartmentOptions, new { @class = "form-select" })
                    </div>
                    <div class="col-md-3">
                        <label for="FromDate" class="form-label">من تاريخ</label>
                        @Html.TextBoxFor(m => m.FromDate, new { @class = "form-control", @type = "date" })
                    </div>
                    <div class="col-md-3">
                        <label for="ToDate" class="form-label">إلى تاريخ</label>
                        @Html.TextBoxFor(m => m.ToDate, new { @class = "form-control", @type = "date" })
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="CivilRecord" class="form-label">السجل المدني</label>
                        <div class="input-group">
                            @Html.TextBoxFor(m => m.CivilRecord, new { @class = "form-control", placeholder = "أدخل السجل المدني" })
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search ml-1"></i>بحث
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter ml-1"></i>تطبيق التصفية
                            </button>
                            @using (Html.BeginForm("ClearFilters", "OrdersManagement", FormMethod.Post, new { style = "display: inline;" }))
                            {
                                @Html.AntiForgeryToken()
                                <button type="submit" class="btn btn-outline-secondary">
                                    <i class="fas fa-times ml-1"></i>إلغاء التصفية
                                </button>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Export Actions -->
    @if (Model.IsAdmin)
    {
        <div class="card-custom mb-4">
            <div class="card-header-custom">
                <h5 class="mb-0"><i class="fas fa-download ml-2"></i>تصدير البيانات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <div class="btn-group flex-wrap" role="group">
                            @using (Html.BeginForm("ExportOrders", "OrdersManagement", FormMethod.Post, new { style = "display: inline;" }))
                            {
                                @Html.AntiForgeryToken()
                                @Html.HiddenFor(m => m.SelectedStatus)
                                @Html.HiddenFor(m => m.SelectedDepartment)
                                @Html.HiddenFor(m => m.FromDate)
                                @Html.HiddenFor(m => m.ToDate)
                                @Html.HiddenFor(m => m.CivilRecord)
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-file-excel ml-1"></i>تصدير الطلبات
                                </button>
                            }
                            
                            @using (Html.BeginForm("ExportFilteredResults", "OrdersManagement", FormMethod.Post, new { style = "display: inline;" }))
                            {
                                @Html.AntiForgeryToken()
                                @Html.HiddenFor(m => m.SelectedStatus)
                                @Html.HiddenFor(m => m.SelectedDepartment)
                                @Html.HiddenFor(m => m.FromDate)
                                @Html.HiddenFor(m => m.ToDate)
                                @Html.HiddenFor(m => m.CivilRecord)
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-filter ml-1"></i>تصدير النتائج المفلترة
                                </button>
                            }
                            
                            @using (Html.BeginForm("ExportDetailedStatistics", "OrdersManagement", FormMethod.Post, new { style = "display: inline;" }))
                            {
                                @Html.AntiForgeryToken()
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-chart-bar ml-1"></i>تصدير التقارير المفصلة
                                </button>
                            }
                            
                            <button type="button" class="btn btn-warning" onclick="generateReports()">
                                <i class="fas fa-cogs ml-1"></i>توليد التقارير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Orders Table -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h5 class="mb-0"><i class="fas fa-list ml-2"></i>قائمة الطلبات (@Model.TotalOrdersCount طلب)</h5>
        </div>
        <div class="card-body">
            @if (Model.Orders.Any())
            {
                <div class="table-responsive">
                    <table class="table table-custom">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>اسم الموظف</th>
                                <th>القسم</th>
                                <th>نوع الطلب</th>
                                <th>حالة الطلب</th>
                                <th>تاريخ الطلب</th>
                                <th>السجل المدني</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in Model.Orders)
                            {
                                <tr>
                                    <td><strong>@order.Id</strong></td>
                                    <td>@order.EmployeeName</td>
                                    <td>@order.Department</td>
                                    <td>@order.OrderType</td>
                                    <td>
                                        <span class="badge bg-primary">@order.OrderStatus</span>
                                    </td>
                                    <td>@order.CreatedAt.ToString("yyyy-MM-dd")</td>
                                    <td>@order.CivilRecord</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h4>لا توجد طلبات</h4>
                    <p>لم يتم العثور على أي طلبات تطابق معايير البحث المحددة</p>
                </div>
            }
        </div>
    </div>

    <!-- Admin Actions (Critical Operations) -->
    @if (Model.IsAdmin)
    {
        <div class="card-custom mt-4">
            <div class="card-header-custom bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle ml-2"></i>إجراءات إدارية حساسة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning ml-2"></i>
                    <strong>تحذير:</strong> هذه الإجراءات حساسة ولا يمكن التراجع عنها. يرجى التأكد قبل المتابعة.
                </div>

                <div class="row">
                    <!-- Delete Old Orders -->
                    <div class="col-md-6">
                        <div class="form-section">
                            <h6><i class="fas fa-trash-alt ml-2"></i>حذف الطلبات القديمة</h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="SelectedDeletePeriod" class="form-label">اختر الفترة</label>
                                    @Html.DropDownListFor(m => m.SelectedDeletePeriod, Model.DeletePeriodOptions,
                                        new { @class = "form-select", @id = "deletePeriodSelect", onchange = "toggleCustomDateRange()" })
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger w-100" onclick="deleteOldOrders()">
                                        <i class="fas fa-trash ml-1"></i>حذف الطلبات
                                    </button>
                                </div>
                            </div>

                            <div id="customDateRange" style="display: none;" class="mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="DeleteStartDate" class="form-label">من تاريخ</label>
                                        @Html.TextBoxFor(m => m.DeleteStartDate, new { @class = "form-control", @type = "date" })
                                    </div>
                                    <div class="col-md-6">
                                        <label for="DeleteEndDate" class="form-label">إلى تاريخ</label>
                                        @Html.TextBoxFor(m => m.DeleteEndDate, new { @class = "form-control", @type = "date" })
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Specific Order -->
                    <div class="col-md-6">
                        <div class="form-section">
                            <h6><i class="fas fa-trash ml-2"></i>حذف طلب محدد</h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="SelectedOrderToDelete" class="form-label">رقم الطلب</label>
                                    @Html.DropDownListFor(m => m.SelectedOrderToDelete, Model.OrderNumbers, "اختر الطلب",
                                        new { @class = "form-select" })
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger w-100" onclick="deleteSpecificOrder()">
                                        <i class="fas fa-trash ml-1"></i>حذف الطلب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        function toggleCustomDateRange() {
            const select = document.getElementById('deletePeriodSelect');
            const customRange = document.getElementById('customDateRange');

            if (select.value === '-1') {
                customRange.style.display = 'block';
            } else {
                customRange.style.display = 'none';
            }
        }

        function deleteOldOrders() {
            if (!confirm('هل أنت متأكد من حذف الطلبات القديمة؟ لا يمكن التراجع عن هذا الإجراء.')) {
                return;
            }

            const formData = new FormData();
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());
            formData.append('SelectedDeletePeriod', $('#deletePeriodSelect').val());
            formData.append('DeleteStartDate', $('#DeleteStartDate').val());
            formData.append('DeleteEndDate', $('#DeleteEndDate').val());

            fetch('@Url.Action("DeleteOldOrders", "OrdersManagement")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حذف الطلبات القديمة بنجاح');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء حذف الطلبات');
                console.error('Error:', error);
            });
        }

        function deleteSpecificOrder() {
            const orderId = $('#SelectedOrderToDelete').val();
            if (!orderId) {
                alert('يرجى اختيار رقم الطلب');
                return;
            }

            if (!confirm('هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.')) {
                return;
            }

            const formData = new FormData();
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());
            formData.append('orderId', orderId);

            fetch('@Url.Action("DeleteSpecificOrder", "OrdersManagement")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حذف الطلب بنجاح');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء حذف الطلب');
                console.error('Error:', error);
            });
        }

        function generateReports() {
            if (!confirm('هل تريد توليد التقارير؟')) {
                return;
            }

            const formData = new FormData();
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

            fetch('@Url.Action("GenerateReports", "OrdersManagement")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم توليد التقارير بنجاح');
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء توليد التقارير');
                console.error('Error:', error);
            });
        }

        // Initialize custom date range visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleCustomDateRange();
        });
    </script>
}
