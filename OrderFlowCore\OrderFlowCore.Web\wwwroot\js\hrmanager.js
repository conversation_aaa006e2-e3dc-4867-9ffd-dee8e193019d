// hrmanager.js
// Handles HR Manager order processing and status change functionality

const HRManager = {
    // State
    currentOrderId: null,
    loadingOverlay: null,
    modals: {},

    // Entry point
    init: function () {
        this.detectPageType();
        this.bindEvents();
        this.initializeModals();
    },

    // Detect which page we're on and initialize accordingly
    detectPageType: function () {
        const path = window.location.pathname.toLowerCase();

        if (path.includes('/hrmanager/processorder/')) {
            this.initProcessOrderPage();
        } else if (path.includes('/hrmanager/changestatus')) {
            this.initChangeStatusPage();
        } else {
            this.initIndexPage();
        }
    },

    // ==================== INDEX PAGE ====================
    initIndexPage: function () {
        // Hide sections initially
        $('#statusChangeSection').hide();
        $('#statusChangeDetailsPanel').hide();
        $('#loading').hide();

        // Set default active filter for status change page
        if ($('button[data-filter="today"]').length > 0) {
            $('button[data-filter="today"]').addClass('active');
        }
    },

    // ==================== PROCESS ORDER PAGE ====================
    initProcessOrderPage: function () {
        // Extract order ID from URL if available
        const pathParts = window.location.pathname.split('/');
        const orderId = pathParts[pathParts.length - 1];

        if (orderId && !isNaN(orderId)) {
            this.currentOrderId = parseInt(orderId);
            this.loadOrderDetails(this.currentOrderId);
        }

        this.updateAutoDeleteStatus();
        this.setupLoadingOverlayOnSubmit();
    },

    // ==================== CHANGE STATUS PAGE ====================
    initChangeStatusPage: function () {
        // Set default active filter
        if ($('button[data-filter="today"]').length > 0) {
            $('button[data-filter="today"]').addClass('active');
        }

        // Load initial orders
        this.loadStatusChangeOrders('', 'today');
    },

    // Bind event handlers
    bindEvents: function () {
        // Main order processing events (only on ProcessOrder page)
        const orderSelect = document.getElementById('orderSelect');
        if (orderSelect) {
            orderSelect.addEventListener('change', () => {
                const orderId = orderSelect.value;
                this.loadOrderDetails(orderId);
            });
        }

        // Action buttons with modal confirmation (only on ProcessOrder page)
        const btnApprove = document.getElementById('btnApprove');
        if (btnApprove) {
            btnApprove.addEventListener('click', () => {
                this.showApproveConfirmation();
            });
        }

        const btnReject = document.getElementById('btnReject');
        if (btnReject) {
            btnReject.addEventListener('click', () => {
                this.showRejectConfirmation();
            });
        }

        const btnReturn = document.getElementById('btnReturn');
        if (btnReturn) {
            btnReturn.addEventListener('click', () => {
                this.showReturnConfirmation();
            });
        }

        const btnDownload = document.getElementById('btnDownload');
        if (btnDownload) {
            btnDownload.addEventListener('click', () => {
                this.downloadAttachments();
            });
        }

        // Modal confirmation buttons
        const confirmApproveBtn = document.getElementById('confirmApproveModalBtn');
        if (confirmApproveBtn) {
            confirmApproveBtn.addEventListener('click', () => {
                this.hideModal('confirmApproveModal');
                this.approveOrder();
            });
        }

        const confirmRejectBtn = document.getElementById('confirmRejectModalBtn');
        if (confirmRejectBtn) {
            confirmRejectBtn.addEventListener('click', () => {
                this.hideModal('confirmRejectModal');
                this.rejectOrder();
            });
        }

        const confirmReturnBtn = document.getElementById('confirmReturnModalBtn');
        if (confirmReturnBtn) {
            confirmReturnBtn.addEventListener('click', () => {
                this.hideModal('confirmReturnModal');
                this.returnOrder();
            });
        }

        // Status change section toggle (only on original Index page)
        const btnToggleStatusChange = document.getElementById('btnToggleStatusChange');
        if (btnToggleStatusChange) {
            btnToggleStatusChange.addEventListener('click', () => {
                $('#statusChangeSection').toggle();
            });
        }

        // Status change events (only on ChangeStatus page)
        const statusChangeOrderSelect = document.getElementById('statusChangeOrderSelect');
        if (statusChangeOrderSelect) {
            statusChangeOrderSelect.addEventListener('change', () => {
                const orderId = statusChangeOrderSelect.value;
                if (orderId) {
                    this.loadStatusChangeOrderDetails(orderId);
                } else {
                    $('#statusChangeDetailsPanel').hide();
                }
            });
        }

        const btnChangeStatus = document.getElementById('btnChangeStatus');
        if (btnChangeStatus) {
            btnChangeStatus.addEventListener('click', () => {
                this.changeOrderStatus();
            });
        }

        // Search and filter events (only on ChangeStatus page)
        const txtSearch = document.getElementById('txtSearch');
        if (txtSearch) {
            txtSearch.addEventListener('input', () => {
                const searchTerm = txtSearch.value;
                const filter = document.querySelector('.btn[data-filter].active')?.dataset.filter || 'today';
                this.loadStatusChangeOrders(searchTerm, filter);
            });
        }

        // Filter buttons
        const filterButtons = document.querySelectorAll('button[data-filter]');
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                document.querySelectorAll('button[data-filter]').forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                const filter = button.dataset.filter;
                const searchTerm = document.getElementById('txtSearch')?.value || '';
                this.loadStatusChangeOrders(searchTerm, filter);
            });
        });

        // Auto delete setting (only on ProcessOrder page)
        const chkAutoDelete = document.getElementById('chkAutoDelete');
        if (chkAutoDelete) {
            chkAutoDelete.addEventListener('change', () => {
                this.updateAutoDeleteStatus();
            });
        }
    },

    // Initialize Bootstrap modals
    initializeModals: function () {
        const modalElements = [
            'confirmApproveModal',
            'confirmRejectModal',
            'confirmReturnModal',
            'confirmStatusChangeModal'
        ];

        modalElements.forEach(modalId => {
            const element = document.getElementById(modalId);
            if (element) {
                this.modals[modalId] = new bootstrap.Modal(element);
            }
        });
    },

    // Modal utility functions
    showModal: function (modalId) {
        if (this.modals[modalId]) {
            this.modals[modalId].show();
        }
    },

    hideModal: function (modalId) {
        if (this.modals[modalId]) {
            this.modals[modalId].hide();
        }
    },

    // Get anti-forgery token
    getAntiForgeryToken: function () {
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        return token ? token.value : '';
    },

    // ==================== MODAL CONFIRMATION FUNCTIONS ====================

    showApproveConfirmation: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        this.showModal('confirmApproveModal');
    },

    showRejectConfirmation: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;
        const rejectReason = document.getElementById('rejectReason')?.value?.trim();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!rejectReason) {
            OrderDetailsModule.showMessage('يرجى إدخال سبب الإلغاء', 'error');
            return;
        }

        // Update modal with the reason
        const rejectReasonModal = document.getElementById('rejectReasonModal');
        if (rejectReasonModal) {
            rejectReasonModal.value = rejectReason;
        }

        this.showModal('confirmRejectModal');
    },

    showReturnConfirmation: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;
        const returnReason = document.getElementById('rejectReason')?.value?.trim();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!returnReason) {
            OrderDetailsModule.showMessage('يرجى إدخال سبب الإعادة', 'error');
            return;
        }

        // Update modal with the reason
        const returnReasonModal = document.getElementById('returnReasonModal');
        if (returnReasonModal) {
            returnReasonModal.value = returnReason;
        }

        this.showModal('confirmReturnModal');
    },

    // ==================== ORDER PROCESSING FUNCTIONS ====================

    loadOrderDetails: function (orderId) {
        if (!orderId) {
            OrderDetailsModule.hideOrderDetails();
            return;
        }

        this.currentOrderId = orderId;
        OrderDetailsModule.loadOrderDetails(orderId, '/HRManager/GetOrderDetails');
    },

    approveOrder: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;
        const autoDelete = document.getElementById('chkAutoDelete')?.checked || false;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        this.showLoading();
        $.post('/HRManager/ApproveOrder', {
            orderId: orderId,
            autoDeleteAttachments: autoDelete,
            __RequestVerificationToken: this.getAntiForgeryToken()
        })
            .done((response) => {
                if (response.success) {
                    OrderDetailsModule.showMessage(response.message, 'success');
                    this.refreshOrderLists();
                    OrderDetailsModule.hideOrderDetails();
                } else {
                    OrderDetailsModule.showMessage(response.message, 'error');
                }
            })
            .fail(() => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء معالجة الطلب', 'error');
            })
            .always(() => {
                this.hideLoading();
            });
    },

    rejectOrder: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;
        const rejectReason = document.getElementById('rejectReason')?.value?.trim();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!rejectReason) {
            OrderDetailsModule.showMessage('يرجى إدخال سبب الإلغاء', 'error');
            return;
        }

        this.showLoading();
        $.post('/HRManager/RejectOrder', {
            orderId: orderId,
            rejectReason: rejectReason,
            __RequestVerificationToken: this.getAntiForgeryToken()
        })
            .done((response) => {
                if (response.success) {
                    OrderDetailsModule.showMessage(response.message, 'success');
                    this.refreshOrderLists();
                    OrderDetailsModule.hideOrderDetails();
                    document.getElementById('rejectReason').value = '';
                } else {
                    OrderDetailsModule.showMessage(response.message, 'error');
                }
            })
            .fail(() => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء معالجة الطلب', 'error');
            })
            .always(() => {
                this.hideLoading();
            });
    },

    returnOrder: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;
        const returnReason = document.getElementById('rejectReason')?.value?.trim();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!returnReason) {
            OrderDetailsModule.showMessage('يرجى إدخال سبب الإعادة', 'error');
            return;
        }

        this.showLoading();
        $.post('/HRManager/ReturnOrder', {
            orderId: orderId,
            returnReason: returnReason,
            __RequestVerificationToken: this.getAntiForgeryToken()
        })
            .done((response) => {
                if (response.success) {
                    OrderDetailsModule.showMessage(response.message, 'success');
                    this.refreshOrderLists();
                    OrderDetailsModule.hideOrderDetails();
                    document.getElementById('rejectReason').value = '';
                } else {
                    OrderDetailsModule.showMessage(response.message, 'error');
                }
            })
            .fail(() => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء معالجة الطلب', 'error');
            })
            .always(() => {
                this.hideLoading();
            });
    },

    downloadAttachments: function () {
        const orderId = document.getElementById('orderSelect')?.value || this.currentOrderId;
        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        window.location.href = '/HRManager/DownloadAttachments?orderId=' + orderId;
    },

    // ==================== STATUS CHANGE FUNCTIONS ====================

    loadStatusChangeOrderDetails: function (orderId) {
        $.post('/HRManager/GetStatusChangeOrderDetails', { orderId: orderId })
            .done((response) => {
                if (response.success) {
                    document.getElementById('lblCurrentStatus').textContent = response.currentStatus;
                    document.getElementById('lblOrderType').textContent = response.orderType;
                    document.getElementById('statusChangeDetailsPanel').style.display = 'block';
                } else {
                    OrderDetailsModule.showMessage(response.message, 'error');
                    document.getElementById('statusChangeDetailsPanel').style.display = 'none';
                }
            })
            .fail(() => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                document.getElementById('statusChangeDetailsPanel').style.display = 'none';
            });
    },

    loadStatusChangeOrders: function (searchTerm, filter) {
        $.post('/HRManager/GetStatusChangeOrders', { searchTerm: searchTerm, filter: filter })
            .done((response) => {
                if (response.success) {
                    const select = document.getElementById('statusChangeOrderSelect');
                    select.innerHTML = '<option value="">-- اختر الطلب --</option>';

                    response.data.forEach(item => {
                        const option = document.createElement('option');
                        option.value = item.value;
                        option.textContent = item.text;
                        select.appendChild(option);
                    });
                } else {
                    OrderDetailsModule.showMessage(response.message, 'error');
                }
            })
            .fail(() => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل الطلبات', 'error');
            });
    },

    changeOrderStatus: function () {
        const orderId = document.getElementById('statusChangeOrderSelect')?.value;
        const newStatus = document.getElementById('newStatusSelect')?.value;
        const notes = document.getElementById('statusChangeNotes')?.value?.trim();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!newStatus) {
            OrderDetailsModule.showMessage('يرجى اختيار الحالة الجديدة', 'error');
            return;
        }

        if (!confirm('هل أنت متأكد من تغيير حالة هذا الطلب؟')) {
            return;
        }

        this.showLoading();
        $.post('/HRManager/ChangeOrderStatus', {
            orderId: orderId,
            newStatus: newStatus,
            notes: notes,
            __RequestVerificationToken: this.getAntiForgeryToken()
        })
            .done((response) => {
                if (response.success) {
                    OrderDetailsModule.showMessage(response.message, 'success');
                    this.refreshOrderLists();
                    document.getElementById('statusChangeNotes').value = '';
                    document.getElementById('newStatusSelect').value = '';
                    document.getElementById('statusChangeDetailsPanel').style.display = 'none';
                } else {
                    OrderDetailsModule.showMessage(response.message, 'error');
                }
            })
            .fail(() => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحديث حالة الطلب', 'error');
            })
            .always(() => {
                this.hideLoading();
            });
    },

    // ==================== UTILITY FUNCTIONS ====================

    updateAutoDeleteStatus: function () {
        const autoDelete = document.getElementById('chkAutoDelete')?.checked || false;
        const statusText = autoDelete ?
            '✓ سيتم حذف المرفقات تلقائياً عند اعتماد الطلب' :
            '✗ لن يتم حذف المرفقات تلقائياً عند اعتماد الطلب';
        const statusClass = autoDelete ? 'text-success' : 'text-danger';

        const statusElement = document.getElementById('autoDeleteStatus');
        if (statusElement) {
            statusElement.textContent = statusText;
            statusElement.className = statusClass;
        }
    },

    refreshOrderLists: function () {
        // Refresh main order list
        location.reload();
    },

    setupLoadingOverlayOnSubmit: function () {
        const forms = ['approveForm', 'rejectForm', 'returnForm'];
        forms.forEach(formId => {
            const form = document.getElementById(formId);
            if (form) {
                form.addEventListener('submit', () => {
                    this.showLoadingOverlay();
                });
            }
        });
    },

    showLoading: function () {
        const element = document.getElementById('loading');
        if (element) element.style.display = 'block';
    },

    hideLoading: function () {
        const element = document.getElementById('loading');
        if (element) element.style.display = 'none';
    },

    showLoadingOverlay: function () {
        if (!this.loadingOverlay) {
            this.loadingOverlay = document.createElement('div');
            this.loadingOverlay.style.position = 'fixed';
            this.loadingOverlay.style.top = 0;
            this.loadingOverlay.style.left = 0;
            this.loadingOverlay.style.width = '100vw';
            this.loadingOverlay.style.height = '100vh';
            this.loadingOverlay.style.background = 'rgba(0,0,0,0.2)';
            this.loadingOverlay.style.zIndex = 2000;
            this.loadingOverlay.innerHTML = '<div class="d-flex justify-content-center align-items-center" style="height:100vh;"><div class="spinner-border text-primary" style="width:3rem;height:3rem;" role="status"><span class="visually-hidden">جاري المعالجة...</span></div></div>';
            document.body.appendChild(this.loadingOverlay);
        }
    },

    hideLoadingOverlay: function () {
        if (this.loadingOverlay) {
            this.loadingOverlay.remove();
            this.loadingOverlay = null;
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    HRManager.init();
});
