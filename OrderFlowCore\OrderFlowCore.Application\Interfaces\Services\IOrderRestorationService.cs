using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrderRestorationService
{
    Task<ServiceResult<List<RestorableOrderDto>>> GetRestorableOrdersAsync(string searchTerm, string filter);
    Task<ServiceResult<RestoreDetailsDto>> GetRestoreOrderDetailsAsync(int orderId);
    Task<ServiceResult> RestoreOrderFromSupervisorsAsync(int orderId, string restoreNotes, string userName);
}
