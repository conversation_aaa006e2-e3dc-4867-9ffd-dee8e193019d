using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface ISupervisorsFollowUpService
    {
        Task<ServiceResult<List<SupervisorsFollowUpDto>>> GetBySupervisorAsync(string supervisorId);
        Task<ServiceResult<SupervisorsFollowUpDto>> GetAsync(string supervisorId, string civilRecord);
        Task<ServiceResult> AddAsync(SupervisorsFollowUpDto record);
        Task<ServiceResult> UpdateAsync(SupervisorsFollowUpDto record);
        Task<ServiceResult> DeleteAsync(string supervisorId, string civilRecord);
        Task<ServiceResult> DeleteAllAsync(string supervisorId);
        Task<ServiceResult<int>> ImportAsync(string supervisorId, System.IO.Stream csvStream);
        Task<ServiceResult<byte[]>> ExportAsync(string supervisorId);
    }
} 