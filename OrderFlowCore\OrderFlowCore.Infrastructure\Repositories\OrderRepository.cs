using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Application.DTOs;
using System;

namespace OrderFlowCore.Infrastructure.Data
{
    public class OrderRepository : IOrderRepository
    {
        private readonly ApplicationDbContext _context;

        public OrderRepository(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        #region Basic CRUD Operations

        public async Task<int> AddAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            await _context.OrdersTables.AddAsync(order);
            return order.Id;
        }

        public async Task<int> UpdateAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            _context.OrdersTables.Update(order);
            return order.Id;
        }

        public async Task<OrdersTable> GetByIdAsync(int id)
        {
            return await _context.OrdersTables.FindAsync(id);
        }

        public async Task<List<OrdersTable>> GetAllAsync()
        {
            return await _context.OrdersTables.ToListAsync();
        }

        public async Task<OrdersTable?> GetOrderByIdAsync(int orderId)
        {
            return await _context.OrdersTables.FindAsync(orderId);
        }

        #endregion

        #region Status-based Queries

        public async Task<List<OrdersTable>> GetDirectMangerPendingOrdersAsync()
        {
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == OrderStatus.DM ||
                           o.OrderStatus == OrderStatus.ReturnedByAssistantManager)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetAssistantManagerPendingOrdersAsync(AssistantManagerType assistantManagerId)
        {
            var status = assistantManagerId.ToOrderStatus();

            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status ||
                           o.OrderStatus == OrderStatus.ReturnedByCoordinator)
                .OrderByDescending(o => o.CreatedAt)
                .ThenByDescending(o => o.Id)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetHRCoordinatorPendingOrdersAsync()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.B,
                OrderStatus.ReturnedBySupervisor,
                OrderStatus.ActionRequired
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetSupervisorsPendingOrdersAsync()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.C,
                OrderStatus.ActionRequiredBySupervisor,
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetHRMangerPendingOrdersAsync()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.D
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersByStatusesAsync(OrderStatus[] statuses)
        {
            if (statuses == null || !statuses.Any())
                return new List<OrdersTable>();

            return await _context.OrdersTables
                .Where(o => statuses.Contains(o.OrderStatus))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersByStatusAsync(OrderStatus status)
        {
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.OrdersTables
                .Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersOlderThanAsync(DateTime cutoffDate)
        {
            return await _context.OrdersTables
                .Where(o => o.CreatedAt < cutoffDate)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task DeleteAsync(int orderId)
        {
            var order = await _context.OrdersTables.FindAsync(orderId);
            if (order != null)
            {
                _context.OrdersTables.Remove(order);
            }
        }

        #endregion
    }
}