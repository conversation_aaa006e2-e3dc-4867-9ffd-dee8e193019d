namespace OrderFlowCore.Core.Entities
{
    public enum SupervisorType
    {
        Unknown,
        EmployeeServices,                   // مشرف خدمات الموظفين
        HumanResourcesPlanning,             // مشرف إدارة تخطيط الموارد البشرية
        InformationTechnology,              // مشرف إدارة تقنية المعلومات
        AttendanceMonitoring,               // مشرف مراقبة الدوام
        MedicalRecords,                     // مشرف السجلات الطبية
        PayrollAndBenefits,                 // مشرف إدارة الرواتب والاستحقاقات
        LegalAndCompliance,                 // مشرف إدارة القانونية والالتزام
        HumanResourcesServices,             // مشرف خدمات الموارد البشرية
        HousingManagement,                  // مشرف إدارة الإسكان
        FilesSection,                       // مشرف قسم الملفات
        OutpatientClinics,                  // مشرف العيادات الخارجية
        SocialInsurance,                    // مشرف التأمينات الاجتماعية
        InventoryMonitoring,                // مشرف وحدة مراقبة المخزون
        RevenueManagement,                  // مشرف إدارة تنمية الإيرادات
        SecurityAndSafety,                  // مشرف إدارة الأمن و السلامة
        Telemedicine                        // مشرف الطب الاتصالي
    }

    public static class SupervisorTypeExtensions
    {
        public static string ToDisplayName(this SupervisorType type)
        {
            return type switch
            {
                SupervisorType.EmployeeServices => "مشرف خدمات الموظفين",
                SupervisorType.HumanResourcesPlanning => "مشرف إدارة تخطيط الموارد البشرية",
                SupervisorType.InformationTechnology => "مشرف إدارة تقنية المعلومات",
                SupervisorType.AttendanceMonitoring => "مشرف مراقبة الدوام",
                SupervisorType.MedicalRecords => "مشرف السجلات الطبية",
                SupervisorType.PayrollAndBenefits => "مشرف إدارة الرواتب والاستحقاقات",
                SupervisorType.LegalAndCompliance => "مشرف إدارة القانونية والالتزام",
                SupervisorType.HumanResourcesServices => "مشرف خدمات الموارد البشرية",
                SupervisorType.HousingManagement => "مشرف إدارة الإسكان",
                SupervisorType.FilesSection => "مشرف قسم الملفات",
                SupervisorType.OutpatientClinics => "مشرف العيادات الخارجية",
                SupervisorType.SocialInsurance => "مشرف التأمينات الاجتماعية",
                SupervisorType.InventoryMonitoring => "مشرف وحدة مراقبة المخزون",
                SupervisorType.RevenueManagement => "مشرف إدارة تنمية الإيرادات",
                SupervisorType.SecurityAndSafety => "مشرف إدارة الأمن و السلامة",
                SupervisorType.Telemedicine => "مشرف الطب الاتصالي",
                _ => "غير محدد"
            };
        }

        public static string ToEnglishName(this SupervisorType type)
        {
            return type switch
            {
                SupervisorType.EmployeeServices => "Employee Services Supervisor",
                SupervisorType.HumanResourcesPlanning => "HR Planning Management Supervisor",
                SupervisorType.InformationTechnology => "IT Management Supervisor",
                SupervisorType.AttendanceMonitoring => "Attendance Monitoring Supervisor",
                SupervisorType.MedicalRecords => "Medical Records Supervisor",
                SupervisorType.PayrollAndBenefits => "Payroll and Benefits Management Supervisor",
                SupervisorType.LegalAndCompliance => "Legal and Compliance Management Supervisor",
                SupervisorType.HumanResourcesServices => "HR Services Supervisor",
                SupervisorType.HousingManagement => "Housing Management Supervisor",
                SupervisorType.FilesSection => "Files Section Supervisor",
                SupervisorType.OutpatientClinics => "Outpatient Clinics Supervisor",
                SupervisorType.SocialInsurance => "Social Insurance Supervisor",
                SupervisorType.InventoryMonitoring => "Inventory Monitoring Unit Supervisor",
                SupervisorType.RevenueManagement => "Revenue Development Management Supervisor",
                SupervisorType.SecurityAndSafety => "Security and Safety Management Supervisor",
                SupervisorType.Telemedicine => "Telemedicine Supervisor",
                _ => "Unknown"
            };
        }
    }
}
