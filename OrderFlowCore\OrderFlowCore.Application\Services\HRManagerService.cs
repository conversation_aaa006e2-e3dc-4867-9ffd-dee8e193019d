using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System.IO.Compression;
using System.IO;
using System.Text;
using OrderFlowCore.Application.Helper;

namespace OrderFlowCore.Application.Services;

public class HRManagerService : IHRManagerService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ISupervisorService _supervisorService;

    public HRManagerService(IUnitOfWork unitOfWork, ISupervisorService supervisorService)
    {
        _unitOfWork = unitOfWork;
        _supervisorService = supervisorService;
    }

    public async Task<ServiceResult<List<OrderSummaryDto>>> GetHRManagerOrdersAsync()
    {
        try
        {
            var orders = await _unitOfWork.Orders.GetHRMangerPendingOrdersAsync();

            var orderSummaries = orders.Select(o => new OrderSummaryDto
            {
                Id = o.Id,
                EmployeeName = o.EmployeeName ?? "",
                Department = o.Department ?? "",
                CreatedAt = o.CreatedAt,
                OrderStatus = o.OrderStatus,
            }).OrderByDescending(o => o.Id).ToList();

            return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء تحميل الطلبات: {ex.Message}");
        }
    }

    public async Task<ServiceResult> ApproveOrderAsync(int orderId, string userName, bool autoDeleteAttachments)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب");
            }

            if (order.OrderStatus != OrderStatus.D)
            {
                return ServiceResult.Failure("الطلب ليس في الحالة المناسبة للاعتماد");
            }

            // Update order status and HR manager approval
            order.OrderStatus = OrderStatus.Accepted;
            order.HumanResourcesManager = OrderHelper.ConfirmedBy(userName);

            // Delete attachments if auto-delete is enabled
            if (autoDeleteAttachments)
            {
                order.File1Url = null;
                order.File2Url = null;
                order.File3Url = null;
                order.File4Url = null;
            }

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            var successMessage = autoDeleteAttachments ?
                "تم قبول الطلب بنجاح وحذف المرفقات" :
                "تم قبول الطلب بنجاح";

            return ServiceResult.Success(successMessage);
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء معالجة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult> RejectOrderAsync(int orderId, string rejectReason, string userName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                return ServiceResult.Failure("يرجى إدخال سبب الإلغاء");
            }

            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب");
            }

            // Update order status
            order.OrderStatus = OrderStatus.CancelledByManager;
            order.ReasonForCancellation = rejectReason;
            order.HumanResourcesManager = OrderHelper.RejectedBy(userName);

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم إلغاء الطلب بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء معالجة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult> ReturnOrderAsync(int orderId, string returnReason, string userName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(returnReason))
            {
                return ServiceResult.Failure("يرجى إدخال سبب الإعادة");
            }

            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب");
            }

            // Update order status
            order.OrderStatus = OrderStatus.ReturnedByManager;
            order.ReasonForCancellation = returnReason;
            order.HumanResourcesManager = OrderHelper.ReturnedBy(userName);

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم إعادة الطلب إلى المنسق بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء معالجة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<OrderSummaryDto>>> GetOrdersForStatusChangeAsync(string searchTerm = "", string filter = "today")
    {
        try
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();

            // Apply date filter
            var filteredOrders = ApplyDateFilter(orders, filter);

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                filteredOrders = filteredOrders.Where(o =>
                    o.Id.ToString().Contains(searchTerm) ||
                    (o.EmployeeName != null && o.EmployeeName.Contains(searchTerm))
                ).ToList();
            }

            var orderSummaries = filteredOrders.Select(o => new OrderSummaryDto
            {
                Id = o.Id,
                EmployeeName = o.EmployeeName ?? "",
                Department = o.Department ?? "",
                CreatedAt = o.CreatedAt,
                OrderStatus = o.OrderStatus
            }).OrderByDescending(o => o.Id).ToList();

            return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء تحميل الطلبات: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<DropdownItemDto>>> GetAvailableStatusesAsync()
    {

        var statuses = new List<DropdownItemDto>
            {
                new() { Value = "", Text = "-- اختر الحالة الجديدة --" },
                new() { Value = OrderStatus.DM.ToDisplayString(), Text = OrderStatus.DM.ToDisplayString() },
                new() { Value = OrderStatus.A1.ToDisplayString(), Text = OrderStatus.A1.ToDisplayString() },
                new() { Value = OrderStatus.A2.ToDisplayString(), Text = OrderStatus.A2.ToDisplayString() },
                new() { Value = OrderStatus.A3.ToDisplayString(), Text = OrderStatus.A3.ToDisplayString() },
                new() { Value = OrderStatus.A4.ToDisplayString(), Text = OrderStatus.A4.ToDisplayString() },
                new() { Value = OrderStatus.B.ToDisplayString(), Text = OrderStatus.B.ToDisplayString() },
                new() { Value = OrderStatus.C.ToDisplayString(), Text = OrderStatus.C.ToDisplayString() },
                new() { Value = OrderStatus.D.ToDisplayString(), Text = OrderStatus.D.ToDisplayString() },
                new() { Value = OrderStatus.Accepted.ToDisplayString(), Text = OrderStatus.Accepted.ToDisplayString() },
                new() { Value = OrderStatus.ReturnedByManager.ToDisplayString(), Text = OrderStatus.ReturnedByManager.ToDisplayString() },
                new() { Value = OrderStatus.CancelledByManager.ToDisplayString(), Text = OrderStatus.CancelledByManager.ToDisplayString() }
            };

        return ServiceResult<List<DropdownItemDto>>.Success(statuses);
    }

    public async Task<ServiceResult> ChangeOrderStatusAsync(int orderId, string newStatus, string notes, string userName)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب المحدد");
            }

            // Parse the new status
            var orderStatus = ParseOrderStatus(newStatus);
            if (orderStatus == null)
            {
                return ServiceResult.Failure("حالة الطلب غير صحيحة");
            }

            // Update order status
            order.OrderStatus = orderStatus.Value;
            order.HumanResourcesManager = $"{DateTime.Now:yyyy-MM-dd} - تم تغيير الحالة إلى {newStatus} بواسطة {userName}";

            // Add notes if provided
            if (!string.IsNullOrWhiteSpace(notes))
            {
                var notesWithDate = $"\n{DateTime.Now:yyyy-MM-dd} - {notes}";
                order.SupervisorNotes = (order.SupervisorNotes ?? "") + notesWithDate;
            }

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم تحديث حالة الطلب بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء تحديث حالة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult<byte[]>> DownloadOrderAttachmentsAsync(int orderId)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
            }

            var employeeName = order.EmployeeName?.Trim() ?? "";
            var filesFound = false;

            using var zipStream = new MemoryStream();
            using (var zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
            {
                var files = new[] { order.File1Url, order.File2Url, order.File3Url, order.File4Url };

                for (int i = 0; i < files.Length; i++)
                {
                    var fileData = files[i];
                    if (fileData != null && fileData.Length > 0)
                    {
                        try
                        {
                            // Extract file data (assuming it's compressed)
                            var extractedData = Array.Empty<byte>();

                            if (extractedData != null && IsPdfFile(extractedData))
                            {
                                filesFound = true;
                                var fileName = CleanFileName($"مرفق_{i + 1}_طلب_{orderId}_{employeeName}.pdf");

                                var zipEntry = zipArchive.CreateEntry(fileName);
                                using var entryStream = zipEntry.Open();
                                entryStream.Write(extractedData, 0, extractedData.Length);
                            }
                        }
                        catch
                        {
                            // Continue with next file if current file fails
                            continue;
                        }
                    }
                }
            }

            if (!filesFound)
            {
                return ServiceResult<byte[]>.Failure("لا توجد ملفات صالحة لتحميلها");
            }

            return ServiceResult<byte[]>.Success(zipStream.ToArray());
        }
        catch (Exception ex)
        {
            return ServiceResult<byte[]>.Failure($"حدث خطأ أثناء تحميل الملفات: {ex.Message}");
        }
    }

    private List<OrdersTable> ApplyDateFilter(IEnumerable<OrdersTable> orders, string filter)
    {
        var now = DateTime.Now;
        var today = now.Date;

        return filter switch
        {
            "today" => orders.Where(o => o.CreatedAt.Date == today).ToList(),
            "week" => orders.Where(o => o.CreatedAt.Date >= today.AddDays(-7)).ToList(),
            "month" => orders.Where(o => o.CreatedAt.Date >= today.AddMonths(-1)).ToList(),
            "two_months" => orders.Where(o => o.CreatedAt.Date >= today.AddMonths(-2)).ToList(),
            "all" => orders.ToList(),
            _ => orders.Where(o => o.CreatedAt.Date == today).ToList()
        };
    }

    private OrderStatus? ParseOrderStatus(string statusString)
    {
        return statusString switch
        {
            "(DM)" => OrderStatus.DM,
            "(A1)" => OrderStatus.A1,
            "(A2)" => OrderStatus.A2,
            "(A3)" => OrderStatus.A3,
            "(A4)" => OrderStatus.A4,
            "(B)" => OrderStatus.B,
            "(C)" => OrderStatus.C,
            "(D)" => OrderStatus.D,
            "مقبول" => OrderStatus.Accepted,
            "أُعيد بواسطة المدير" => OrderStatus.ReturnedByManager,
            "تم الإلغاء من قبل المدير" => OrderStatus.CancelledByManager,
            _ => null
        };
    }

    private byte[]? ExtractFileData(byte[] compressedData)
    {
        try
        {
            // Implement file extraction logic based on your compression method
            // This is a placeholder - implement according to your FileCompressor.ExtractFile method
            return compressedData;
        }
        catch
        {
            return null;
        }
    }

    private bool IsPdfFile(byte[] data)
    {
        if (data == null || data.Length < 4)
            return false;

        return data[0] == 0x25 && // %
               data[1] == 0x50 && // P
               data[2] == 0x44 && // D
               data[3] == 0x46;   // F
    }

    private string CleanFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        foreach (char c in invalidChars)
        {
            fileName = fileName.Replace(c, '_');
        }

        const int maxLength = 100;
        if (fileName.Length > maxLength)
        {
            string extension = Path.GetExtension(fileName);
            fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
        }

        return fileName;
    }
}
