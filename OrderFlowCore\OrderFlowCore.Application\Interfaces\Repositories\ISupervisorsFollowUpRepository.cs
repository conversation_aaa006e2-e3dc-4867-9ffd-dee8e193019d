using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface ISupervisorsFollowUpRepository
    {
        Task<List<SupervisorsFollowUp>> GetBySupervisorAsync(string supervisorId);
        Task<SupervisorsFollowUp> GetAsync(string supervisorId, string civilRecord);
        Task AddAsync(SupervisorsFollowUp entity);
        Task UpdateAsync(SupervisorsFollowUp entity);
        Task DeleteAsync(string supervisorId, string civilRecord);
        Task DeleteAllAsync(string supervisorId);
        Task<bool> AnyAsync(string supervisorId, string civilRecord);
        Task<List<SupervisorsFollowUp>> GetAllAsync();
    }
} 