using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Common;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IOrdersManagementService
    {
        // Order Filtering and Retrieval
        Task<ServiceResult<List<OrderSummaryDto>>> GetFilteredOrdersAsync(OrderFilterDto filter);
        Task<ServiceResult<List<OrderSummaryDto>>> GetAllOrdersAsync();
        Task<ServiceResult<List<DropdownItemDto>>> GetOrderStatusesAsync();
        Task<ServiceResult<List<DropdownItemDto>>> GetDepartmentsAsync();
        Task<ServiceResult<List<DropdownItemDto>>> GetOrderNumbersAsync();

        // Export Operations
        Task<ServiceResult<byte[]>> ExportOrdersToExcelAsync(OrderFilterDto filter);
        Task<ServiceResult<byte[]>> ExportFilteredResultsToExcelAsync(OrderFilterDto filter);
        Task<ServiceResult<byte[]>> ExportDetailedStatisticsToExcelAsync();

        // Admin Operations (Critical Actions)
        Task<ServiceResult> DeleteOldOrdersAsync(DeleteOldOrdersDto deleteRequest, string userName);
        Task<ServiceResult> DeleteSpecificOrderAsync(int orderId, string userName);

        // Report Generation
        Task<ServiceResult> GenerateReportsAsync(string userName);
    }

    // DTOs for the service
    public class OrderFilterDto
    {
        public string Status { get; set; } = "All";
        public string Department { get; set; } = "All";
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string CivilRecord { get; set; } = string.Empty;
    }

    public class DeleteOldOrdersDto
    {
        public int PeriodInMonths { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool UseCustomDateRange { get; set; }
    }

    public class DropdownItemDto
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }
}
