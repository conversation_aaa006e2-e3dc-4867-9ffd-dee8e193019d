using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class AutoRoutingRepository : IAutoRoutingRepository
    {
        private readonly ApplicationDbContext _context;

        public AutoRoutingRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<AutoRouting>> GetAllAsync()
        {
            return await _context.AutoRoutings
                .OrderByDescending(ar => ar.CreatedAt)
                .ToListAsync();
        }

        public async Task<AutoRouting?> GetByIdAsync(int id)
        {
            return await _context.AutoRoutings.FindAsync(id);
        }

        public async Task<AutoRouting> AddAsync(AutoRouting entity)
        {
            entity.CreatedAt = DateTime.UtcNow;
            _context.AutoRoutings.Add(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        public async Task UpdateAsync(AutoRouting entity)
        {
            entity.ModifiedAt = DateTime.UtcNow;
            _context.AutoRoutings.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var entity = await GetByIdAsync(id);
            if (entity != null)
            {
                _context.AutoRoutings.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(string orderType, string nationality, string job, int? excludeId = null)
        {
            var query = _context.AutoRoutings
                .Where(ar => ar.OrderType == orderType && 
                           ar.Nationality == nationality && 
                           ar.Status == true);

            if (excludeId.HasValue)
            {
                query = query.Where(ar => ar.Id != excludeId.Value);
            }

            // Check for job 
            var count = await query.CountAsync(ar =>
                ar.Job == "الكل" ||
                ar.Job == job);

            return count > 0;
        }

        public async Task<int> GetActiveCountAsync()
        {
            return await _context.AutoRoutings.CountAsync(ar => ar.Status == true);
        }

        public async Task<List<AutoRouting>> GetActiveRoutesAsync()
        {
            return await _context.AutoRoutings
                .Where(ar => ar.Status == true)
                .OrderByDescending(ar => ar.CreatedAt)
                .ToListAsync();
        }

        public async Task<AutoRouting?> GetMatchingRouteAsync(string orderType, string nationality, string job)
        {
            return await _context.AutoRoutings
                .Where(ar => ar.OrderType == orderType && 
                           ar.Nationality == nationality && 
                           ar.Status == true)
                .Where(ar =>
                    ar.Job == "الكل" ||
                    ar.Job == job)
                .FirstOrDefaultAsync();
        }
    }
}
