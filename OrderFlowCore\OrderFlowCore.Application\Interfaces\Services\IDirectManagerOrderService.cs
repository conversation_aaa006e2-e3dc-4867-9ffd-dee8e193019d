using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IDirectManagerOrderService
{
    Task<ServiceResult<List<OrderSummaryDto>>> GetPendingOrdersForDirectMangerAsync();
    Task<ServiceResult> ConfirmOrderByDirectManagerAsync(int orderId, string? userName);
    Task<ServiceResult> RejectOrderByDirectManagerAsync(int orderId, string reason, string? userName);
}
